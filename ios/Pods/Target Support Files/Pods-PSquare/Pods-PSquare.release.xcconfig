CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) SD_WEBP=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/BVLinearGradient" "${PODS_ROOT}/Headers/Public/DoubleConversion" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/RCT-Folly" "${PODS_ROOT}/Headers/Public/RCTDeprecation" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/RCTTypeSafety" "${PODS_ROOT}/Headers/Public/RNCAsyncStorage" "${PODS_ROOT}/Headers/Public/RNCClipboard" "${PODS_ROOT}/Headers/Public/RNCMaskedView" "${PODS_ROOT}/Headers/Public/RNCPicker" "${PODS_ROOT}/Headers/Public/RNDateTimePicker" "${PODS_ROOT}/Headers/Public/RNFS" "${PODS_ROOT}/Headers/Public/RNFastImage" "${PODS_ROOT}/Headers/Public/RNFileViewer" "${PODS_ROOT}/Headers/Public/RNGestureHandler" "${PODS_ROOT}/Headers/Public/RNPermissions" "${PODS_ROOT}/Headers/Public/RNReanimated" "${PODS_ROOT}/Headers/Public/RNSVG" "${PODS_ROOT}/Headers/Public/RNScreens" "${PODS_ROOT}/Headers/Public/RNShare" "${PODS_ROOT}/Headers/Public/RNSound" "${PODS_ROOT}/Headers/Public/RNVectorIcons" "${PODS_ROOT}/Headers/Public/React-Core" "${PODS_ROOT}/Headers/Public/React-Fabric" "${PODS_ROOT}/Headers/Public/React-FabricComponents" "${PODS_ROOT}/Headers/Public/React-FabricImage" "${PODS_ROOT}/Headers/Public/React-ImageManager" "${PODS_ROOT}/Headers/Public/React-Mapbuffer" "${PODS_ROOT}/Headers/Public/React-NativeModulesApple" "${PODS_ROOT}/Headers/Public/React-RCTAnimation" "${PODS_ROOT}/Headers/Public/React-RCTAppDelegate" "${PODS_ROOT}/Headers/Public/React-RCTBlob" "${PODS_ROOT}/Headers/Public/React-RCTFabric" "${PODS_ROOT}/Headers/Public/React-RCTText" "${PODS_ROOT}/Headers/Public/React-RuntimeApple" "${PODS_ROOT}/Headers/Public/React-RuntimeCore" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-cxxreact" "${PODS_ROOT}/Headers/Public/React-debug" "${PODS_ROOT}/Headers/Public/React-defaultsnativemodule" "${PODS_ROOT}/Headers/Public/React-domnativemodule" "${PODS_ROOT}/Headers/Public/React-featureflags" "${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule" "${PODS_ROOT}/Headers/Public/React-graphics" "${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule" "${PODS_ROOT}/Headers/Public/React-jsc" "${PODS_ROOT}/Headers/Public/React-jserrorhandler" "${PODS_ROOT}/Headers/Public/React-jsi" "${PODS_ROOT}/Headers/Public/React-jsiexecutor" "${PODS_ROOT}/Headers/Public/React-jsinspector" "${PODS_ROOT}/Headers/Public/React-logger" "${PODS_ROOT}/Headers/Public/React-microtasksnativemodule" "${PODS_ROOT}/Headers/Public/React-nativeconfig" "${PODS_ROOT}/Headers/Public/React-perflogger" "${PODS_ROOT}/Headers/Public/React-performancetimeline" "${PODS_ROOT}/Headers/Public/React-rendererconsistency" "${PODS_ROOT}/Headers/Public/React-rendererdebug" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-runtimescheduler" "${PODS_ROOT}/Headers/Public/React-utils" "${PODS_ROOT}/Headers/Public/ReactCodegen" "${PODS_ROOT}/Headers/Public/ReactCommon" "${PODS_ROOT}/Headers/Public/SDWebImage" "${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder" "${PODS_ROOT}/Headers/Public/SocketRocket" "${PODS_ROOT}/Headers/Public/Yoga" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fmt" "${PODS_ROOT}/Headers/Public/glog" "${PODS_ROOT}/Headers/Public/libwebp" "${PODS_ROOT}/Headers/Public/react-native-blob-util" "${PODS_ROOT}/Headers/Public/react-native-camera" "${PODS_ROOT}/Headers/Public/react-native-document-picker" "${PODS_ROOT}/Headers/Public/react-native-geolocation" "${PODS_ROOT}/Headers/Public/react-native-html-to-pdf" "${PODS_ROOT}/Headers/Public/react-native-maps" "${PODS_ROOT}/Headers/Public/react-native-pager-view" "${PODS_ROOT}/Headers/Public/react-native-safe-area-context" "${PODS_ROOT}/Headers/Public/react-native-slider" "${PODS_ROOT}/Headers/Public/react-native-splash-screen" "$(PODS_ROOT)/DoubleConversion" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost-for-react-native" "$(PODS_ROOT)/glog" "$(PODS_ROOT)/RCT-Folly" "$(PODS_ROOT)/Headers/Public/React-hermes" "$(PODS_ROOT)/Headers/Public/hermes-engine" "$(PODS_ROOT)/../../node_modules/react-native/ReactCommon" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/apple" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/Common/cpp" "$(PODS_ROOT)/Headers/Private/React-Core"
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient" "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard" "${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView" "${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker" "${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker" "${PODS_CONFIGURATION_BUILD_DIR}/RNFS" "${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage" "${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer" "${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions" "${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "${PODS_CONFIGURATION_BUILD_DIR}/RNSVG" "${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "${PODS_CONFIGURATION_BUILD_DIR}/RNShare" "${PODS_CONFIGURATION_BUILD_DIR}/RNSound" "${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsc" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/fmt" "${PODS_CONFIGURATION_BUILD_DIR}/glog" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-camera" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-maps" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-pager-view" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen"
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap" $(inherited)  $(inherited) -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32   -DREACT_NATIVE_MINOR_VERSION=75 -DREANIMATED_VERSION=3.17.3   $(inherited) -DREACT_NATIVE_MINOR_VERSION=75
OTHER_LDFLAGS = $(inherited) -ObjC -l"BVLinearGradient" -l"DoubleConversion" -l"RCT-Folly" -l"RCTDeprecation" -l"RCTTypeSafety" -l"RNCAsyncStorage" -l"RNCClipboard" -l"RNCMaskedView" -l"RNCPicker" -l"RNDateTimePicker" -l"RNFS" -l"RNFastImage" -l"RNFileViewer" -l"RNGestureHandler" -l"RNPermissions" -l"RNReanimated" -l"RNSVG" -l"RNScreens" -l"RNShare" -l"RNSound" -l"RNVectorIcons" -l"React-Core" -l"React-CoreModules" -l"React-Fabric" -l"React-FabricComponents" -l"React-FabricImage" -l"React-ImageManager" -l"React-Mapbuffer" -l"React-NativeModulesApple" -l"React-RCTAnimation" -l"React-RCTAppDelegate" -l"React-RCTBlob" -l"React-RCTFabric" -l"React-RCTImage" -l"React-RCTLinking" -l"React-RCTNetwork" -l"React-RCTSettings" -l"React-RCTText" -l"React-RCTVibration" -l"React-RuntimeApple" -l"React-RuntimeCore" -l"React-cxxreact" -l"React-debug" -l"React-defaultsnativemodule" -l"React-domnativemodule" -l"React-featureflags" -l"React-featureflagsnativemodule" -l"React-graphics" -l"React-idlecallbacksnativemodule" -l"React-jsc" -l"React-jserrorhandler" -l"React-jsi" -l"React-jsiexecutor" -l"React-jsinspector" -l"React-logger" -l"React-microtasksnativemodule" -l"React-nativeconfig" -l"React-perflogger" -l"React-performancetimeline" -l"React-rendererconsistency" -l"React-rendererdebug" -l"React-runtimescheduler" -l"React-utils" -l"ReactCodegen" -l"ReactCommon" -l"SDWebImage" -l"SDWebImageWebPCoder" -l"SocketRocket" -l"Yoga" -l"c++" -l"c++abi" -l"fmt" -l"glog" -l"icucore" -l"libwebp" -l"react-native-blob-util" -l"react-native-camera" -l"react-native-document-picker" -l"react-native-geolocation" -l"react-native-html-to-pdf" -l"react-native-maps" -l"react-native-pager-view" -l"react-native-safe-area-context" -l"react-native-slider" -l"react-native-splash-screen" -framework "AVFoundation" -framework "Accelerate" -framework "AssetsLibrary" -framework "AudioToolbox" -framework "CFNetwork" -framework "CoreLocation" -framework "Foundation" -framework "ImageIO" -framework "JavaScriptCore" -framework "MediaPlayer" -framework "MobileCoreServices" -framework "Photos" -framework "PhotosUI" -framework "QuartzCore" -framework "Security" -framework "UIKit" -framework "UserNotifications" -weak_framework "LinkPresentation"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient" "-F${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBLazyVector" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTRequired" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFS" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNSVG" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNShare" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNSound" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons" "-F${PODS_CONFIGURATION_BUILD_DIR}/React" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTActionSheet" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-callinvoker" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsc" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsitracing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rncore" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-runtimeexecutor" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "-F${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "-F${PODS_CONFIGURATION_BUILD_DIR}/boost" "-F${PODS_CONFIGURATION_BUILD_DIR}/fmt" "-F${PODS_CONFIGURATION_BUILD_DIR}/glog" "-F${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-camera" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-maps" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-pager-view" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USER_HEADER_SEARCH_PATHS = $(inherited) $(SRCROOT)/libwebp/src
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
