/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of FBReactNativeSpec symbols
#ifndef FBReactNativeSpec_H
#define FBReactNativeSpec_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>


@protocol NativeAccessibilityInfoSpec <RCTBridgeModule, RCTTurboModule>

- (void)isReduceMotionEnabled:(RCTResponseSenderBlock)onSuccess;
- (void)isTouchExplorationEnabled:(RCTResponseSenderBlock)onSuccess;
- (void)isAccessibilityServiceEnabled:(RCTResponseSenderBlock)onSuccess;
- (void)setAccessibilityFocus:(double)reactTag;
- (void)announceForAccessibility:(NSString *)announcement;
- (void)getRecommendedTimeoutMillis:(double)mSec
                          onSuccess:(RCTResponseSenderBlock)onSuccess;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAccessibilityInfo'
   */
  class JSI_EXPORT NativeAccessibilityInfoSpecJSI : public ObjCTurboModule {
  public:
    NativeAccessibilityInfoSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeAccessibilityManager {
    struct SpecSetAccessibilityContentSizeMultipliersJSMultipliers {
      std::optional<double> extraSmall() const;
      std::optional<double> small() const;
      std::optional<double> medium() const;
      std::optional<double> large() const;
      std::optional<double> extraLarge() const;
      std::optional<double> extraExtraLarge() const;
      std::optional<double> extraExtraExtraLarge() const;
      std::optional<double> accessibilityMedium() const;
      std::optional<double> accessibilityLarge() const;
      std::optional<double> accessibilityExtraLarge() const;
      std::optional<double> accessibilityExtraExtraLarge() const;
      std::optional<double> accessibilityExtraExtraExtraLarge() const;

      SpecSetAccessibilityContentSizeMultipliersJSMultipliers(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers)
+ (RCTManagedPointer *)JS_NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers:(id)json;
@end
namespace JS {
  namespace NativeAccessibilityManager {
    struct SpecAnnounceForAccessibilityWithOptionsOptions {
      std::optional<bool> queue() const;

      SpecAnnounceForAccessibilityWithOptionsOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAccessibilityManager_SpecAnnounceForAccessibilityWithOptionsOptions)
+ (RCTManagedPointer *)JS_NativeAccessibilityManager_SpecAnnounceForAccessibilityWithOptionsOptions:(id)json;
@end
@protocol NativeAccessibilityManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)getCurrentBoldTextState:(RCTResponseSenderBlock)onSuccess
                        onError:(RCTResponseSenderBlock)onError;
- (void)getCurrentGrayscaleState:(RCTResponseSenderBlock)onSuccess
                         onError:(RCTResponseSenderBlock)onError;
- (void)getCurrentInvertColorsState:(RCTResponseSenderBlock)onSuccess
                            onError:(RCTResponseSenderBlock)onError;
- (void)getCurrentReduceMotionState:(RCTResponseSenderBlock)onSuccess
                            onError:(RCTResponseSenderBlock)onError;
- (void)getCurrentPrefersCrossFadeTransitionsState:(RCTResponseSenderBlock)onSuccess
                                           onError:(RCTResponseSenderBlock)onError;
- (void)getCurrentReduceTransparencyState:(RCTResponseSenderBlock)onSuccess
                                  onError:(RCTResponseSenderBlock)onError;
- (void)getCurrentVoiceOverState:(RCTResponseSenderBlock)onSuccess
                         onError:(RCTResponseSenderBlock)onError;
- (void)setAccessibilityContentSizeMultipliers:(JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers &)JSMultipliers;
- (void)setAccessibilityFocus:(double)reactTag;
- (void)announceForAccessibility:(NSString *)announcement;
- (void)announceForAccessibilityWithOptions:(NSString *)announcement
                                    options:(JS::NativeAccessibilityManager::SpecAnnounceForAccessibilityWithOptionsOptions &)options;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAccessibilityManager'
   */
  class JSI_EXPORT NativeAccessibilityManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeAccessibilityManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeActionSheetManager {
    struct SpecShowActionSheetWithOptionsOptions {
      NSString *title() const;
      NSString *message() const;
      std::optional<facebook::react::LazyVector<NSString *>> options() const;
      std::optional<facebook::react::LazyVector<double>> destructiveButtonIndices() const;
      std::optional<double> cancelButtonIndex() const;
      std::optional<double> anchor() const;
      std::optional<double> tintColor() const;
      std::optional<double> cancelButtonTintColor() const;
      NSString *userInterfaceStyle() const;
      std::optional<facebook::react::LazyVector<double>> disabledButtonIndices() const;

      SpecShowActionSheetWithOptionsOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions)
+ (RCTManagedPointer *)JS_NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions:(id)json;
@end
namespace JS {
  namespace NativeActionSheetManager {
    struct SpecShowShareActionSheetWithOptionsOptions {
      NSString *message() const;
      NSString *url() const;
      NSString *subject() const;
      std::optional<double> anchor() const;
      std::optional<double> tintColor() const;
      std::optional<double> cancelButtonTintColor() const;
      std::optional<facebook::react::LazyVector<NSString *>> excludedActivityTypes() const;
      NSString *userInterfaceStyle() const;

      SpecShowShareActionSheetWithOptionsOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions)
+ (RCTManagedPointer *)JS_NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions:(id)json;
@end
@protocol NativeActionSheetManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)showActionSheetWithOptions:(JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions &)options
                          callback:(RCTResponseSenderBlock)callback;
- (void)showShareActionSheetWithOptions:(JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions &)options
                        failureCallback:(RCTResponseSenderBlock)failureCallback
                        successCallback:(RCTResponseSenderBlock)successCallback;
- (void)dismissActionSheet;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeActionSheetManager'
   */
  class JSI_EXPORT NativeActionSheetManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeActionSheetManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeAlertManager {
    struct Args {
      NSString *title() const;
      NSString *message() const;
      std::optional<facebook::react::LazyVector<id<NSObject>>> buttons() const;
      NSString *type() const;
      NSString *defaultValue() const;
      NSString *cancelButtonKey() const;
      NSString *destructiveButtonKey() const;
      NSString *preferredButtonKey() const;
      NSString *keyboardType() const;
      NSString *userInterfaceStyle() const;

      Args(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAlertManager_Args)
+ (RCTManagedPointer *)JS_NativeAlertManager_Args:(id)json;
@end
@protocol NativeAlertManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)alertWithArgs:(JS::NativeAlertManager::Args &)args
             callback:(RCTResponseSenderBlock)callback;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAlertManager'
   */
  class JSI_EXPORT NativeAlertManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeAlertManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeAnimatedModule {
    struct EventMapping {
      facebook::react::LazyVector<NSString *> nativeEventPath() const;
      std::optional<double> animatedValueTag() const;

      EventMapping(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAnimatedModule_EventMapping)
+ (RCTManagedPointer *)JS_NativeAnimatedModule_EventMapping:(id)json;
@end
@protocol NativeAnimatedModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)startOperationBatch;
- (void)finishOperationBatch;
- (void)createAnimatedNode:(double)tag
                    config:(NSDictionary *)config;
- (void)updateAnimatedNodeConfig:(double)tag
                          config:(NSDictionary *)config;
- (void)getValue:(double)tag
saveValueCallback:(RCTResponseSenderBlock)saveValueCallback;
- (void)startListeningToAnimatedNodeValue:(double)tag;
- (void)stopListeningToAnimatedNodeValue:(double)tag;
- (void)connectAnimatedNodes:(double)parentTag
                    childTag:(double)childTag;
- (void)disconnectAnimatedNodes:(double)parentTag
                       childTag:(double)childTag;
- (void)startAnimatingNode:(double)animationId
                   nodeTag:(double)nodeTag
                    config:(NSDictionary *)config
               endCallback:(RCTResponseSenderBlock)endCallback;
- (void)stopAnimation:(double)animationId;
- (void)setAnimatedNodeValue:(double)nodeTag
                       value:(double)value;
- (void)setAnimatedNodeOffset:(double)nodeTag
                       offset:(double)offset;
- (void)flattenAnimatedNodeOffset:(double)nodeTag;
- (void)extractAnimatedNodeOffset:(double)nodeTag;
- (void)connectAnimatedNodeToView:(double)nodeTag
                          viewTag:(double)viewTag;
- (void)disconnectAnimatedNodeFromView:(double)nodeTag
                               viewTag:(double)viewTag;
- (void)restoreDefaultValues:(double)nodeTag;
- (void)dropAnimatedNode:(double)tag;
- (void)addAnimatedEventToView:(double)viewTag
                     eventName:(NSString *)eventName
                  eventMapping:(JS::NativeAnimatedModule::EventMapping &)eventMapping;
- (void)removeAnimatedEventFromView:(double)viewTag
                          eventName:(NSString *)eventName
                    animatedNodeTag:(double)animatedNodeTag;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;
- (void)queueAndExecuteBatchedOperations:(NSArray *)operationsAndArgs;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAnimatedModule'
   */
  class JSI_EXPORT NativeAnimatedModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeAnimatedModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeAnimatedTurboModule {
    struct EventMapping {
      facebook::react::LazyVector<NSString *> nativeEventPath() const;
      std::optional<double> animatedValueTag() const;

      EventMapping(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAnimatedTurboModule_EventMapping)
+ (RCTManagedPointer *)JS_NativeAnimatedTurboModule_EventMapping:(id)json;
@end
@protocol NativeAnimatedTurboModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)startOperationBatch;
- (void)finishOperationBatch;
- (void)createAnimatedNode:(double)tag
                    config:(NSDictionary *)config;
- (void)updateAnimatedNodeConfig:(double)tag
                          config:(NSDictionary *)config;
- (void)getValue:(double)tag
saveValueCallback:(RCTResponseSenderBlock)saveValueCallback;
- (void)startListeningToAnimatedNodeValue:(double)tag;
- (void)stopListeningToAnimatedNodeValue:(double)tag;
- (void)connectAnimatedNodes:(double)parentTag
                    childTag:(double)childTag;
- (void)disconnectAnimatedNodes:(double)parentTag
                       childTag:(double)childTag;
- (void)startAnimatingNode:(double)animationId
                   nodeTag:(double)nodeTag
                    config:(NSDictionary *)config
               endCallback:(RCTResponseSenderBlock)endCallback;
- (void)stopAnimation:(double)animationId;
- (void)setAnimatedNodeValue:(double)nodeTag
                       value:(double)value;
- (void)setAnimatedNodeOffset:(double)nodeTag
                       offset:(double)offset;
- (void)flattenAnimatedNodeOffset:(double)nodeTag;
- (void)extractAnimatedNodeOffset:(double)nodeTag;
- (void)connectAnimatedNodeToView:(double)nodeTag
                          viewTag:(double)viewTag;
- (void)disconnectAnimatedNodeFromView:(double)nodeTag
                               viewTag:(double)viewTag;
- (void)restoreDefaultValues:(double)nodeTag;
- (void)dropAnimatedNode:(double)tag;
- (void)addAnimatedEventToView:(double)viewTag
                     eventName:(NSString *)eventName
                  eventMapping:(JS::NativeAnimatedTurboModule::EventMapping &)eventMapping;
- (void)removeAnimatedEventFromView:(double)viewTag
                          eventName:(NSString *)eventName
                    animatedNodeTag:(double)animatedNodeTag;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;
- (void)queueAndExecuteBatchedOperations:(NSArray *)operationsAndArgs;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAnimatedTurboModule'
   */
  class JSI_EXPORT NativeAnimatedTurboModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeAnimatedTurboModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeAppState {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<NSString *> initialAppState;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeAppStateSpec <RCTBridgeModule, RCTTurboModule>

- (void)getCurrentAppState:(RCTResponseSenderBlock)success
                     error:(RCTResponseSenderBlock)error;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;
- (facebook::react::ModuleConstants<JS::NativeAppState::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeAppState::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAppState'
   */
  class JSI_EXPORT NativeAppStateSpecJSI : public ObjCTurboModule {
  public:
    NativeAppStateSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeAppearanceSpec <RCTBridgeModule, RCTTurboModule>

- (NSString * _Nullable)getColorScheme;
- (void)setColorScheme:(NSString *)colorScheme;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAppearance'
   */
  class JSI_EXPORT NativeAppearanceSpecJSI : public ObjCTurboModule {
  public:
    NativeAppearanceSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeBlobModule {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<NSString *> BLOB_URI_SCHEME;
          RCTRequired<NSString *> BLOB_URI_HOST;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeBlobModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)addNetworkingHandler;
- (void)addWebSocketHandler:(double)id;
- (void)removeWebSocketHandler:(double)id;
- (void)sendOverSocket:(NSDictionary *)blob
              socketID:(double)socketID;
- (void)createFromParts:(NSArray *)parts
                 withId:(NSString *)withId;
- (void)release:(NSString *)blobId;
- (facebook::react::ModuleConstants<JS::NativeBlobModule::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeBlobModule::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeBlobModule'
   */
  class JSI_EXPORT NativeBlobModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeBlobModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeBugReportingSpec <RCTBridgeModule, RCTTurboModule>

- (void)startReportAProblemFlow;
- (void)setExtraData:(NSDictionary *)extraData
          extraFiles:(NSDictionary *)extraFiles;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeBugReporting'
   */
  class JSI_EXPORT NativeBugReportingSpecJSI : public ObjCTurboModule {
  public:
    NativeBugReportingSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeClipboardSpec <RCTBridgeModule, RCTTurboModule>

- (void)getString:(RCTPromiseResolveBlock)resolve
           reject:(RCTPromiseRejectBlock)reject;
- (void)setString:(NSString *)content;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeClipboard'
   */
  class JSI_EXPORT NativeClipboardSpecJSI : public ObjCTurboModule {
  public:
    NativeClipboardSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeDevLoadingViewSpec <RCTBridgeModule, RCTTurboModule>

- (void)showMessage:(NSString *)message
          withColor:(NSNumber *)withColor
withBackgroundColor:(NSNumber *)withBackgroundColor;
- (void)hide;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeDevLoadingView'
   */
  class JSI_EXPORT NativeDevLoadingViewSpecJSI : public ObjCTurboModule {
  public:
    NativeDevLoadingViewSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeDevMenuSpec <RCTBridgeModule, RCTTurboModule>

- (void)show;
- (void)reload;
- (void)debugRemotely:(BOOL)enableDebug;
- (void)setProfilingEnabled:(BOOL)enabled;
- (void)setHotLoadingEnabled:(BOOL)enabled;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeDevMenu'
   */
  class JSI_EXPORT NativeDevMenuSpecJSI : public ObjCTurboModule {
  public:
    NativeDevMenuSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeDevSettingsSpec <RCTBridgeModule, RCTTurboModule>

- (void)reload;
- (void)reloadWithReason:(NSString *)reason;
- (void)onFastRefresh;
- (void)setHotLoadingEnabled:(BOOL)isHotLoadingEnabled;
- (void)setIsDebuggingRemotely:(BOOL)isDebuggingRemotelyEnabled;
- (void)setProfilingEnabled:(BOOL)isProfilingEnabled;
- (void)toggleElementInspector;
- (void)addMenuItem:(NSString *)title;
- (void)openDebugger;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;
- (void)setIsShakeToShowDevMenuEnabled:(BOOL)enabled;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeDevSettings'
   */
  class JSI_EXPORT NativeDevSettingsSpecJSI : public ObjCTurboModule {
  public:
    NativeDevSettingsSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeDevToolsSettingsManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)setConsolePatchSettings:(NSString *)newConsolePatchSettings;
- (NSString * _Nullable)getConsolePatchSettings;
- (void)setProfilingSettings:(NSString *)newProfilingSettings;
- (NSString * _Nullable)getProfilingSettings;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeDevToolsSettingsManager'
   */
  class JSI_EXPORT NativeDevToolsSettingsManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeDevToolsSettingsManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeDeviceEventManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)invokeDefaultBackPressHandler;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeDeviceEventManager'
   */
  class JSI_EXPORT NativeDeviceEventManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeDeviceEventManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeDeviceInfo {
    struct DisplayMetrics {

      struct Builder {
        struct Input {
          RCTRequired<double> width;
          RCTRequired<double> height;
          RCTRequired<double> scale;
          RCTRequired<double> fontScale;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing DisplayMetrics */
        Builder(DisplayMetrics i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static DisplayMetrics fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      DisplayMetrics(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativeDeviceInfo {
    struct DisplayMetricsAndroid {

      struct Builder {
        struct Input {
          RCTRequired<double> width;
          RCTRequired<double> height;
          RCTRequired<double> scale;
          RCTRequired<double> fontScale;
          RCTRequired<double> densityDpi;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing DisplayMetricsAndroid */
        Builder(DisplayMetricsAndroid i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static DisplayMetricsAndroid fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      DisplayMetricsAndroid(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativeDeviceInfo {
    struct DimensionsPayload {

      struct Builder {
        struct Input {
          std::optional<JS::NativeDeviceInfo::DisplayMetrics::Builder> window;
          std::optional<JS::NativeDeviceInfo::DisplayMetrics::Builder> screen;
          std::optional<JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder> windowPhysicalPixels;
          std::optional<JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder> screenPhysicalPixels;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing DimensionsPayload */
        Builder(DimensionsPayload i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static DimensionsPayload fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      DimensionsPayload(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativeDeviceInfo {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<JS::NativeDeviceInfo::DimensionsPayload::Builder> Dimensions;
          std::optional<bool> isIPhoneX_deprecated;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeDeviceInfoSpec <RCTBridgeModule, RCTTurboModule>

- (facebook::react::ModuleConstants<JS::NativeDeviceInfo::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeDeviceInfo::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeDeviceInfo'
   */
  class JSI_EXPORT NativeDeviceInfoSpecJSI : public ObjCTurboModule {
  public:
    NativeDeviceInfoSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeExceptionsManager {
    struct StackFrame {
      std::optional<double> column() const;
      NSString *file() const;
      std::optional<double> lineNumber() const;
      NSString *methodName() const;
      std::optional<bool> collapse() const;

      StackFrame(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeExceptionsManager_StackFrame)
+ (RCTManagedPointer *)JS_NativeExceptionsManager_StackFrame:(id)json;
@end
namespace JS {
  namespace NativeExceptionsManager {
    struct ExceptionData {
      NSString *message() const;
      NSString *originalMessage() const;
      NSString *name() const;
      NSString *componentStack() const;
      facebook::react::LazyVector<JS::NativeExceptionsManager::StackFrame> stack() const;
      double id_() const;
      bool isFatal() const;
      id<NSObject> _Nullable extraData() const;

      ExceptionData(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeExceptionsManager_ExceptionData)
+ (RCTManagedPointer *)JS_NativeExceptionsManager_ExceptionData:(id)json;
@end
@protocol NativeExceptionsManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)reportFatalException:(NSString *)message
                       stack:(NSArray *)stack
                 exceptionId:(double)exceptionId;
- (void)reportSoftException:(NSString *)message
                      stack:(NSArray *)stack
                exceptionId:(double)exceptionId;
- (void)reportException:(JS::NativeExceptionsManager::ExceptionData &)data;
- (void)updateExceptionMessage:(NSString *)message
                         stack:(NSArray *)stack
                   exceptionId:(double)exceptionId;
- (void)dismissRedbox;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeExceptionsManager'
   */
  class JSI_EXPORT NativeExceptionsManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeExceptionsManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeFileReaderModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)readAsDataURL:(NSDictionary *)data
              resolve:(RCTPromiseResolveBlock)resolve
               reject:(RCTPromiseRejectBlock)reject;
- (void)readAsText:(NSDictionary *)data
          encoding:(NSString *)encoding
           resolve:(RCTPromiseResolveBlock)resolve
            reject:(RCTPromiseRejectBlock)reject;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeFileReaderModule'
   */
  class JSI_EXPORT NativeFileReaderModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeFileReaderModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeFrameRateLogger {
    struct SpecSetGlobalOptionsOptions {
      std::optional<bool> debug() const;

      SpecSetGlobalOptionsOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeFrameRateLogger_SpecSetGlobalOptionsOptions)
+ (RCTManagedPointer *)JS_NativeFrameRateLogger_SpecSetGlobalOptionsOptions:(id)json;
@end
@protocol NativeFrameRateLoggerSpec <RCTBridgeModule, RCTTurboModule>

- (void)setGlobalOptions:(JS::NativeFrameRateLogger::SpecSetGlobalOptionsOptions &)options;
- (void)setContext:(NSString *)context;
- (void)beginScroll;
- (void)endScroll;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeFrameRateLogger'
   */
  class JSI_EXPORT NativeFrameRateLoggerSpecJSI : public ObjCTurboModule {
  public:
    NativeFrameRateLoggerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeHeadlessJsTaskSupportSpec <RCTBridgeModule, RCTTurboModule>

- (void)notifyTaskFinished:(double)taskId;
- (void)notifyTaskRetry:(double)taskId
                resolve:(RCTPromiseResolveBlock)resolve
                 reject:(RCTPromiseRejectBlock)reject;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeHeadlessJsTaskSupport'
   */
  class JSI_EXPORT NativeHeadlessJsTaskSupportSpecJSI : public ObjCTurboModule {
  public:
    NativeHeadlessJsTaskSupportSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeI18nManager {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<bool> doLeftAndRightSwapInRTL;
          RCTRequired<bool> isRTL;
          NSString *localeIdentifier;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeI18nManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)allowRTL:(BOOL)allowRTL;
- (void)forceRTL:(BOOL)forceRTL;
- (void)swapLeftAndRightInRTL:(BOOL)flipStyles;
- (facebook::react::ModuleConstants<JS::NativeI18nManager::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeI18nManager::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeI18nManager'
   */
  class JSI_EXPORT NativeI18nManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeI18nManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeImageEditor {
    struct OptionsOffset {
      double x() const;
      double y() const;

      OptionsOffset(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeImageEditor_OptionsOffset)
+ (RCTManagedPointer *)JS_NativeImageEditor_OptionsOffset:(id)json;
@end
namespace JS {
  namespace NativeImageEditor {
    struct OptionsSize {
      double width() const;
      double height() const;

      OptionsSize(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeImageEditor_OptionsSize)
+ (RCTManagedPointer *)JS_NativeImageEditor_OptionsSize:(id)json;
@end
namespace JS {
  namespace NativeImageEditor {
    struct OptionsDisplaySize {
      double width() const;
      double height() const;

      OptionsDisplaySize(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeImageEditor_OptionsDisplaySize)
+ (RCTManagedPointer *)JS_NativeImageEditor_OptionsDisplaySize:(id)json;
@end
namespace JS {
  namespace NativeImageEditor {
    struct Options {
      JS::NativeImageEditor::OptionsOffset offset() const;
      JS::NativeImageEditor::OptionsSize size() const;
      std::optional<JS::NativeImageEditor::OptionsDisplaySize> displaySize() const;
      NSString *resizeMode() const;
      std::optional<bool> allowExternalStorage() const;

      Options(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeImageEditor_Options)
+ (RCTManagedPointer *)JS_NativeImageEditor_Options:(id)json;
@end
@protocol NativeImageEditorSpec <RCTBridgeModule, RCTTurboModule>

- (void)cropImage:(NSString *)uri
         cropData:(JS::NativeImageEditor::Options &)cropData
  successCallback:(RCTResponseSenderBlock)successCallback
    errorCallback:(RCTResponseSenderBlock)errorCallback;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeImageEditor'
   */
  class JSI_EXPORT NativeImageEditorSpecJSI : public ObjCTurboModule {
  public:
    NativeImageEditorSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeImageLoaderIOSSpec <RCTBridgeModule, RCTTurboModule>

- (void)getSize:(NSString *)uri
        resolve:(RCTPromiseResolveBlock)resolve
         reject:(RCTPromiseRejectBlock)reject;
- (void)getSizeWithHeaders:(NSString *)uri
                   headers:(NSDictionary *)headers
                   resolve:(RCTPromiseResolveBlock)resolve
                    reject:(RCTPromiseRejectBlock)reject;
- (void)prefetchImage:(NSString *)uri
              resolve:(RCTPromiseResolveBlock)resolve
               reject:(RCTPromiseRejectBlock)reject;
- (void)prefetchImageWithMetadata:(NSString *)uri
                    queryRootName:(NSString *)queryRootName
                          rootTag:(double)rootTag
                          resolve:(RCTPromiseResolveBlock)resolve
                           reject:(RCTPromiseRejectBlock)reject;
- (void)queryCache:(NSArray *)uris
           resolve:(RCTPromiseResolveBlock)resolve
            reject:(RCTPromiseRejectBlock)reject;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeImageLoaderIOS'
   */
  class JSI_EXPORT NativeImageLoaderIOSSpecJSI : public ObjCTurboModule {
  public:
    NativeImageLoaderIOSSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeImageStoreIOSSpec <RCTBridgeModule, RCTTurboModule>

- (void)getBase64ForTag:(NSString *)uri
        successCallback:(RCTResponseSenderBlock)successCallback
          errorCallback:(RCTResponseSenderBlock)errorCallback;
- (void)hasImageForTag:(NSString *)uri
              callback:(RCTResponseSenderBlock)callback;
- (void)removeImageForTag:(NSString *)uri;
- (void)addImageFromBase64:(NSString *)base64ImageData
           successCallback:(RCTResponseSenderBlock)successCallback
             errorCallback:(RCTResponseSenderBlock)errorCallback;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeImageStoreIOS'
   */
  class JSI_EXPORT NativeImageStoreIOSSpecJSI : public ObjCTurboModule {
  public:
    NativeImageStoreIOSSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeJSCHeapCaptureSpec <RCTBridgeModule, RCTTurboModule>

- (void)captureComplete:(NSString *)path
                  error:(NSString * _Nullable)error;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeJSCHeapCapture'
   */
  class JSI_EXPORT NativeJSCHeapCaptureSpecJSI : public ObjCTurboModule {
  public:
    NativeJSCHeapCaptureSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeJSCSamplingProfilerSpec <RCTBridgeModule, RCTTurboModule>

- (void)operationComplete:(double)token
                   result:(NSString * _Nullable)result
                    error:(NSString * _Nullable)error;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeJSCSamplingProfiler'
   */
  class JSI_EXPORT NativeJSCSamplingProfilerSpecJSI : public ObjCTurboModule {
  public:
    NativeJSCSamplingProfilerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeKeyboardObserverSpec <RCTBridgeModule, RCTTurboModule>

- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeKeyboardObserver'
   */
  class JSI_EXPORT NativeKeyboardObserverSpecJSI : public ObjCTurboModule {
  public:
    NativeKeyboardObserverSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeLinkingManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)getInitialURL:(RCTPromiseResolveBlock)resolve
               reject:(RCTPromiseRejectBlock)reject;
- (void)canOpenURL:(NSString *)url
           resolve:(RCTPromiseResolveBlock)resolve
            reject:(RCTPromiseRejectBlock)reject;
- (void)openURL:(NSString *)url
        resolve:(RCTPromiseResolveBlock)resolve
         reject:(RCTPromiseRejectBlock)reject;
- (void)openSettings:(RCTPromiseResolveBlock)resolve
              reject:(RCTPromiseRejectBlock)reject;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeLinkingManager'
   */
  class JSI_EXPORT NativeLinkingManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeLinkingManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeLogBoxSpec <RCTBridgeModule, RCTTurboModule>

- (void)show;
- (void)hide;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeLogBox'
   */
  class JSI_EXPORT NativeLogBoxSpecJSI : public ObjCTurboModule {
  public:
    NativeLogBoxSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeModalManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeModalManager'
   */
  class JSI_EXPORT NativeModalManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeModalManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeNetworkingIOS {
    struct SpecSendRequestQuery {
      NSString *method() const;
      NSString *url() const;
      id<NSObject> data() const;
      id<NSObject> headers() const;
      NSString *responseType() const;
      bool incrementalUpdates() const;
      double timeout() const;
      bool withCredentials() const;

      SpecSendRequestQuery(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeNetworkingIOS_SpecSendRequestQuery)
+ (RCTManagedPointer *)JS_NativeNetworkingIOS_SpecSendRequestQuery:(id)json;
@end
@protocol NativeNetworkingIOSSpec <RCTBridgeModule, RCTTurboModule>

- (void)sendRequest:(JS::NativeNetworkingIOS::SpecSendRequestQuery &)query
           callback:(RCTResponseSenderBlock)callback;
- (void)abortRequest:(double)requestId;
- (void)clearCookies:(RCTResponseSenderBlock)callback;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeNetworkingIOS'
   */
  class JSI_EXPORT NativeNetworkingIOSSpecJSI : public ObjCTurboModule {
  public:
    NativeNetworkingIOSSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativePlatformConstantsIOS {
    struct ConstantsReactNativeVersion {

      struct Builder {
        struct Input {
          RCTRequired<double> major;
          RCTRequired<double> minor;
          RCTRequired<double> patch;
          RCTRequired<NSString *> prerelease;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing ConstantsReactNativeVersion */
        Builder(ConstantsReactNativeVersion i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static ConstantsReactNativeVersion fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      ConstantsReactNativeVersion(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativePlatformConstantsIOS {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<bool> isTesting;
          std::optional<bool> isDisableAnimations;
          RCTRequired<JS::NativePlatformConstantsIOS::ConstantsReactNativeVersion::Builder> reactNativeVersion;
          RCTRequired<bool> forceTouchAvailable;
          RCTRequired<NSString *> osVersion;
          RCTRequired<NSString *> systemName;
          RCTRequired<NSString *> interfaceIdiom;
          std::optional<bool> isMacCatalyst;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativePlatformConstantsIOSSpec <RCTBridgeModule, RCTTurboModule>

- (facebook::react::ModuleConstants<JS::NativePlatformConstantsIOS::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativePlatformConstantsIOS::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativePlatformConstantsIOS'
   */
  class JSI_EXPORT NativePlatformConstantsIOSSpecJSI : public ObjCTurboModule {
  public:
    NativePlatformConstantsIOSSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativePushNotificationManagerIOS {
    struct SpecRequestPermissionsPermission {
      bool alert() const;
      bool badge() const;
      bool sound() const;

      SpecRequestPermissionsPermission(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativePushNotificationManagerIOS_SpecRequestPermissionsPermission)
+ (RCTManagedPointer *)JS_NativePushNotificationManagerIOS_SpecRequestPermissionsPermission:(id)json;
@end
namespace JS {
  namespace NativePushNotificationManagerIOS {
    struct Notification {
      NSString *alertTitle() const;
      NSString *alertBody() const;
      id<NSObject> _Nullable userInfo() const;
      NSString *category() const;
      std::optional<double> fireDate() const;
      std::optional<double> fireIntervalSeconds() const;
      std::optional<double> applicationIconBadgeNumber() const;
      std::optional<bool> isSilent() const;
      NSString *soundName() const;

      Notification(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativePushNotificationManagerIOS_Notification)
+ (RCTManagedPointer *)JS_NativePushNotificationManagerIOS_Notification:(id)json;
@end
@protocol NativePushNotificationManagerIOSSpec <RCTBridgeModule, RCTTurboModule>

- (void)onFinishRemoteNotification:(NSString *)notificationId
                       fetchResult:(NSString *)fetchResult;
- (void)setApplicationIconBadgeNumber:(double)num;
- (void)getApplicationIconBadgeNumber:(RCTResponseSenderBlock)callback;
- (void)requestPermissions:(JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission &)permission
                   resolve:(RCTPromiseResolveBlock)resolve
                    reject:(RCTPromiseRejectBlock)reject;
- (void)abandonPermissions;
- (void)checkPermissions:(RCTResponseSenderBlock)callback;
- (void)presentLocalNotification:(JS::NativePushNotificationManagerIOS::Notification &)notification;
- (void)scheduleLocalNotification:(JS::NativePushNotificationManagerIOS::Notification &)notification;
- (void)cancelAllLocalNotifications;
- (void)cancelLocalNotifications:(NSDictionary *)userInfo;
- (void)getInitialNotification:(RCTPromiseResolveBlock)resolve
                        reject:(RCTPromiseRejectBlock)reject;
- (void)getScheduledLocalNotifications:(RCTResponseSenderBlock)callback;
- (void)removeAllDeliveredNotifications;
- (void)removeDeliveredNotifications:(NSArray *)identifiers;
- (void)getDeliveredNotifications:(RCTResponseSenderBlock)callback;
- (void)getAuthorizationStatus:(RCTResponseSenderBlock)callback;
- (void)addListener:(NSString *)eventType;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativePushNotificationManagerIOS'
   */
  class JSI_EXPORT NativePushNotificationManagerIOSSpecJSI : public ObjCTurboModule {
  public:
    NativePushNotificationManagerIOSSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeRedBoxSpec <RCTBridgeModule, RCTTurboModule>

- (void)setExtraData:(NSDictionary *)extraData
       forIdentifier:(NSString *)forIdentifier;
- (void)dismiss;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeRedBox'
   */
  class JSI_EXPORT NativeRedBoxSpecJSI : public ObjCTurboModule {
  public:
    NativeRedBoxSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeSegmentFetcherSpec <RCTBridgeModule, RCTTurboModule>

- (void)fetchSegment:(double)segmentId
             options:(NSDictionary *)options
            callback:(RCTResponseSenderBlock)callback;
- (void)getSegment:(double)segmentId
           options:(NSDictionary *)options
          callback:(RCTResponseSenderBlock)callback;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeSegmentFetcher'
   */
  class JSI_EXPORT NativeSegmentFetcherSpecJSI : public ObjCTurboModule {
  public:
    NativeSegmentFetcherSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeSettingsManager {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<id<NSObject>> settings;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeSettingsManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)setValues:(NSDictionary *)values;
- (void)deleteValues:(NSArray *)values;
- (facebook::react::ModuleConstants<JS::NativeSettingsManager::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeSettingsManager::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeSettingsManager'
   */
  class JSI_EXPORT NativeSettingsManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeSettingsManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeShareModule {
    struct SpecShareContent {
      NSString *title() const;
      NSString *message() const;

      SpecShareContent(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeShareModule_SpecShareContent)
+ (RCTManagedPointer *)JS_NativeShareModule_SpecShareContent:(id)json;
@end
@protocol NativeShareModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)share:(JS::NativeShareModule::SpecShareContent &)content
  dialogTitle:(NSString *)dialogTitle
      resolve:(RCTPromiseResolveBlock)resolve
       reject:(RCTPromiseRejectBlock)reject;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeShareModule'
   */
  class JSI_EXPORT NativeShareModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeShareModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeSoundManagerSpec <RCTBridgeModule, RCTTurboModule>

- (void)playTouchSound;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeSoundManager'
   */
  class JSI_EXPORT NativeSoundManagerSpecJSI : public ObjCTurboModule {
  public:
    NativeSoundManagerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeSourceCode {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<NSString *> scriptURL;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeSourceCodeSpec <RCTBridgeModule, RCTTurboModule>

- (facebook::react::ModuleConstants<JS::NativeSourceCode::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeSourceCode::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeSourceCode'
   */
  class JSI_EXPORT NativeSourceCodeSpecJSI : public ObjCTurboModule {
  public:
    NativeSourceCodeSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeStatusBarManagerIOS {
    struct Constants {

      struct Builder {
        struct Input {
          RCTRequired<double> HEIGHT;
          std::optional<double> DEFAULT_BACKGROUND_COLOR;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeStatusBarManagerIOSSpec <RCTBridgeModule, RCTTurboModule>

- (void)getHeight:(RCTResponseSenderBlock)callback;
- (void)setNetworkActivityIndicatorVisible:(BOOL)visible;
- (void)addListener:(NSString *)eventType;
- (void)removeListeners:(double)count;
- (void)setStyle:(NSString * _Nullable)statusBarStyle
        animated:(BOOL)animated;
- (void)setHidden:(BOOL)hidden
    withAnimation:(NSString *)withAnimation;
- (facebook::react::ModuleConstants<JS::NativeStatusBarManagerIOS::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeStatusBarManagerIOS::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeStatusBarManagerIOS'
   */
  class JSI_EXPORT NativeStatusBarManagerIOSSpecJSI : public ObjCTurboModule {
  public:
    NativeStatusBarManagerIOSSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeTimingSpec <RCTBridgeModule, RCTTurboModule>

- (void)createTimer:(double)callbackID
           duration:(double)duration
   jsSchedulingTime:(double)jsSchedulingTime
            repeats:(BOOL)repeats;
- (void)deleteTimer:(double)timerID;
- (void)setSendIdleEvents:(BOOL)sendIdleEvents;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeTiming'
   */
  class JSI_EXPORT NativeTimingSpecJSI : public ObjCTurboModule {
  public:
    NativeTimingSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

@protocol NativeVibrationSpec <RCTBridgeModule, RCTTurboModule>

- (void)vibrate:(double)pattern;
- (void)vibrateByPattern:(NSArray *)pattern
                  repeat:(double)repeat;
- (void)cancel;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeVibration'
   */
  class JSI_EXPORT NativeVibrationSpecJSI : public ObjCTurboModule {
  public:
    NativeVibrationSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeWebSocketModule {
    struct SpecConnectOptions {
      id<NSObject> _Nullable headers() const;

      SpecConnectOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeWebSocketModule_SpecConnectOptions)
+ (RCTManagedPointer *)JS_NativeWebSocketModule_SpecConnectOptions:(id)json;
@end
@protocol NativeWebSocketModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)connect:(NSString *)url
      protocols:(NSArray * _Nullable)protocols
        options:(JS::NativeWebSocketModule::SpecConnectOptions &)options
       socketID:(double)socketID;
- (void)send:(NSString *)message
 forSocketID:(double)forSocketID;
- (void)sendBinary:(NSString *)base64String
       forSocketID:(double)forSocketID;
- (void)ping:(double)socketID;
- (void)close:(double)code
       reason:(NSString *)reason
     socketID:(double)socketID;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeWebSocketModule'
   */
  class JSI_EXPORT NativeWebSocketModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeWebSocketModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react

inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraSmall() const
{
  id const p = _v[@"extraSmall"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::small() const
{
  id const p = _v[@"small"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::medium() const
{
  id const p = _v[@"medium"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::large() const
{
  id const p = _v[@"large"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraLarge() const
{
  id const p = _v[@"extraLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraExtraLarge() const
{
  id const p = _v[@"extraExtraLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraExtraExtraLarge() const
{
  id const p = _v[@"extraExtraExtraLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityMedium() const
{
  id const p = _v[@"accessibilityMedium"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityLarge() const
{
  id const p = _v[@"accessibilityLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityExtraLarge() const
{
  id const p = _v[@"accessibilityExtraLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityExtraExtraLarge() const
{
  id const p = _v[@"accessibilityExtraExtraLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityExtraExtraExtraLarge() const
{
  id const p = _v[@"accessibilityExtraExtraExtraLarge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<bool> JS::NativeAccessibilityManager::SpecAnnounceForAccessibilityWithOptionsOptions::queue() const
{
  id const p = _v[@"queue"];
  return RCTBridgingToOptionalBool(p);
}
inline NSString *JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::title() const
{
  id const p = _v[@"title"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::message() const
{
  id const p = _v[@"message"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<facebook::react::LazyVector<NSString *>> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::options() const
{
  id const p = _v[@"options"];
  return RCTBridgingToOptionalVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
}
inline std::optional<facebook::react::LazyVector<double>> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::destructiveButtonIndices() const
{
  id const p = _v[@"destructiveButtonIndices"];
  return RCTBridgingToOptionalVec(p, ^double(id itemValue_0) { return RCTBridgingToDouble(itemValue_0); });
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::cancelButtonIndex() const
{
  id const p = _v[@"cancelButtonIndex"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::anchor() const
{
  id const p = _v[@"anchor"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::tintColor() const
{
  id const p = _v[@"tintColor"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::cancelButtonTintColor() const
{
  id const p = _v[@"cancelButtonTintColor"];
  return RCTBridgingToOptionalDouble(p);
}
inline NSString *JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::userInterfaceStyle() const
{
  id const p = _v[@"userInterfaceStyle"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<facebook::react::LazyVector<double>> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::disabledButtonIndices() const
{
  id const p = _v[@"disabledButtonIndices"];
  return RCTBridgingToOptionalVec(p, ^double(id itemValue_0) { return RCTBridgingToDouble(itemValue_0); });
}
inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::message() const
{
  id const p = _v[@"message"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::url() const
{
  id const p = _v[@"url"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::subject() const
{
  id const p = _v[@"subject"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::anchor() const
{
  id const p = _v[@"anchor"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::tintColor() const
{
  id const p = _v[@"tintColor"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::cancelButtonTintColor() const
{
  id const p = _v[@"cancelButtonTintColor"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<facebook::react::LazyVector<NSString *>> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::excludedActivityTypes() const
{
  id const p = _v[@"excludedActivityTypes"];
  return RCTBridgingToOptionalVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
}
inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::userInterfaceStyle() const
{
  id const p = _v[@"userInterfaceStyle"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::title() const
{
  id const p = _v[@"title"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::message() const
{
  id const p = _v[@"message"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<facebook::react::LazyVector<id<NSObject>>> JS::NativeAlertManager::Args::buttons() const
{
  id const p = _v[@"buttons"];
  return RCTBridgingToOptionalVec(p, ^id<NSObject>(id itemValue_0) { return itemValue_0; });
}
inline NSString *JS::NativeAlertManager::Args::type() const
{
  id const p = _v[@"type"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::defaultValue() const
{
  id const p = _v[@"defaultValue"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::cancelButtonKey() const
{
  id const p = _v[@"cancelButtonKey"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::destructiveButtonKey() const
{
  id const p = _v[@"destructiveButtonKey"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::preferredButtonKey() const
{
  id const p = _v[@"preferredButtonKey"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::keyboardType() const
{
  id const p = _v[@"keyboardType"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeAlertManager::Args::userInterfaceStyle() const
{
  id const p = _v[@"userInterfaceStyle"];
  return RCTBridgingToOptionalString(p);
}
inline facebook::react::LazyVector<NSString *> JS::NativeAnimatedModule::EventMapping::nativeEventPath() const
{
  id const p = _v[@"nativeEventPath"];
  return RCTBridgingToVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
}
inline std::optional<double> JS::NativeAnimatedModule::EventMapping::animatedValueTag() const
{
  id const p = _v[@"animatedValueTag"];
  return RCTBridgingToOptionalDouble(p);
}
inline facebook::react::LazyVector<NSString *> JS::NativeAnimatedTurboModule::EventMapping::nativeEventPath() const
{
  id const p = _v[@"nativeEventPath"];
  return RCTBridgingToVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
}
inline std::optional<double> JS::NativeAnimatedTurboModule::EventMapping::animatedValueTag() const
{
  id const p = _v[@"animatedValueTag"];
  return RCTBridgingToOptionalDouble(p);
}
inline JS::NativeAppState::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto initialAppState = i.initialAppState.get();
  d[@"initialAppState"] = initialAppState;
  return d;
}) {}
inline JS::NativeAppState::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}

inline JS::NativeBlobModule::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto BLOB_URI_SCHEME = i.BLOB_URI_SCHEME.get();
  d[@"BLOB_URI_SCHEME"] = BLOB_URI_SCHEME;
  auto BLOB_URI_HOST = i.BLOB_URI_HOST.get();
  d[@"BLOB_URI_HOST"] = BLOB_URI_HOST;
  return d;
}) {}
inline JS::NativeBlobModule::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}







inline JS::NativeDeviceInfo::DisplayMetrics::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto width = i.width.get();
  d[@"width"] = @(width);
  auto height = i.height.get();
  d[@"height"] = @(height);
  auto scale = i.scale.get();
  d[@"scale"] = @(scale);
  auto fontScale = i.fontScale.get();
  d[@"fontScale"] = @(fontScale);
  return d;
}) {}
inline JS::NativeDeviceInfo::DisplayMetrics::Builder::Builder(DisplayMetrics i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto width = i.width.get();
  d[@"width"] = @(width);
  auto height = i.height.get();
  d[@"height"] = @(height);
  auto scale = i.scale.get();
  d[@"scale"] = @(scale);
  auto fontScale = i.fontScale.get();
  d[@"fontScale"] = @(fontScale);
  auto densityDpi = i.densityDpi.get();
  d[@"densityDpi"] = @(densityDpi);
  return d;
}) {}
inline JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder::Builder(DisplayMetricsAndroid i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeDeviceInfo::DimensionsPayload::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto window = i.window;
  d[@"window"] = window.has_value() ? window.value().buildUnsafeRawValue() : nil;
  auto screen = i.screen;
  d[@"screen"] = screen.has_value() ? screen.value().buildUnsafeRawValue() : nil;
  auto windowPhysicalPixels = i.windowPhysicalPixels;
  d[@"windowPhysicalPixels"] = windowPhysicalPixels.has_value() ? windowPhysicalPixels.value().buildUnsafeRawValue() : nil;
  auto screenPhysicalPixels = i.screenPhysicalPixels;
  d[@"screenPhysicalPixels"] = screenPhysicalPixels.has_value() ? screenPhysicalPixels.value().buildUnsafeRawValue() : nil;
  return d;
}) {}
inline JS::NativeDeviceInfo::DimensionsPayload::Builder::Builder(DimensionsPayload i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeDeviceInfo::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto Dimensions = i.Dimensions.get();
  d[@"Dimensions"] = Dimensions.buildUnsafeRawValue();
  auto isIPhoneX_deprecated = i.isIPhoneX_deprecated;
  d[@"isIPhoneX_deprecated"] = isIPhoneX_deprecated.has_value() ? @((BOOL)isIPhoneX_deprecated.value()) : nil;
  return d;
}) {}
inline JS::NativeDeviceInfo::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline std::optional<double> JS::NativeExceptionsManager::StackFrame::column() const
{
  id const p = _v[@"column"];
  return RCTBridgingToOptionalDouble(p);
}
inline NSString *JS::NativeExceptionsManager::StackFrame::file() const
{
  id const p = _v[@"file"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<double> JS::NativeExceptionsManager::StackFrame::lineNumber() const
{
  id const p = _v[@"lineNumber"];
  return RCTBridgingToOptionalDouble(p);
}
inline NSString *JS::NativeExceptionsManager::StackFrame::methodName() const
{
  id const p = _v[@"methodName"];
  return RCTBridgingToString(p);
}
inline std::optional<bool> JS::NativeExceptionsManager::StackFrame::collapse() const
{
  id const p = _v[@"collapse"];
  return RCTBridgingToOptionalBool(p);
}
inline NSString *JS::NativeExceptionsManager::ExceptionData::message() const
{
  id const p = _v[@"message"];
  return RCTBridgingToString(p);
}
inline NSString *JS::NativeExceptionsManager::ExceptionData::originalMessage() const
{
  id const p = _v[@"originalMessage"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeExceptionsManager::ExceptionData::name() const
{
  id const p = _v[@"name"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeExceptionsManager::ExceptionData::componentStack() const
{
  id const p = _v[@"componentStack"];
  return RCTBridgingToOptionalString(p);
}
inline facebook::react::LazyVector<JS::NativeExceptionsManager::StackFrame> JS::NativeExceptionsManager::ExceptionData::stack() const
{
  id const p = _v[@"stack"];
  return RCTBridgingToVec(p, ^JS::NativeExceptionsManager::StackFrame(id itemValue_0) { return JS::NativeExceptionsManager::StackFrame(itemValue_0); });
}
inline double JS::NativeExceptionsManager::ExceptionData::id_() const
{
  id const p = _v[@"id"];
  return RCTBridgingToDouble(p);
}
inline bool JS::NativeExceptionsManager::ExceptionData::isFatal() const
{
  id const p = _v[@"isFatal"];
  return RCTBridgingToBool(p);
}
inline id<NSObject> _Nullable JS::NativeExceptionsManager::ExceptionData::extraData() const
{
  id const p = _v[@"extraData"];
  return p;
}

inline std::optional<bool> JS::NativeFrameRateLogger::SpecSetGlobalOptionsOptions::debug() const
{
  id const p = _v[@"debug"];
  return RCTBridgingToOptionalBool(p);
}

inline JS::NativeI18nManager::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto doLeftAndRightSwapInRTL = i.doLeftAndRightSwapInRTL.get();
  d[@"doLeftAndRightSwapInRTL"] = @(doLeftAndRightSwapInRTL);
  auto isRTL = i.isRTL.get();
  d[@"isRTL"] = @(isRTL);
  auto localeIdentifier = i.localeIdentifier;
  d[@"localeIdentifier"] = localeIdentifier;
  return d;
}) {}
inline JS::NativeI18nManager::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline double JS::NativeImageEditor::OptionsOffset::x() const
{
  id const p = _v[@"x"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeImageEditor::OptionsOffset::y() const
{
  id const p = _v[@"y"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeImageEditor::OptionsSize::width() const
{
  id const p = _v[@"width"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeImageEditor::OptionsSize::height() const
{
  id const p = _v[@"height"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeImageEditor::OptionsDisplaySize::width() const
{
  id const p = _v[@"width"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeImageEditor::OptionsDisplaySize::height() const
{
  id const p = _v[@"height"];
  return RCTBridgingToDouble(p);
}
inline JS::NativeImageEditor::OptionsOffset JS::NativeImageEditor::Options::offset() const
{
  id const p = _v[@"offset"];
  return JS::NativeImageEditor::OptionsOffset(p);
}
inline JS::NativeImageEditor::OptionsSize JS::NativeImageEditor::Options::size() const
{
  id const p = _v[@"size"];
  return JS::NativeImageEditor::OptionsSize(p);
}
inline std::optional<JS::NativeImageEditor::OptionsDisplaySize> JS::NativeImageEditor::Options::displaySize() const
{
  id const p = _v[@"displaySize"];
  return (p == nil ? std::nullopt : std::make_optional(JS::NativeImageEditor::OptionsDisplaySize(p)));
}
inline NSString *JS::NativeImageEditor::Options::resizeMode() const
{
  id const p = _v[@"resizeMode"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<bool> JS::NativeImageEditor::Options::allowExternalStorage() const
{
  id const p = _v[@"allowExternalStorage"];
  return RCTBridgingToOptionalBool(p);
}








inline NSString *JS::NativeNetworkingIOS::SpecSendRequestQuery::method() const
{
  id const p = _v[@"method"];
  return RCTBridgingToString(p);
}
inline NSString *JS::NativeNetworkingIOS::SpecSendRequestQuery::url() const
{
  id const p = _v[@"url"];
  return RCTBridgingToString(p);
}
inline id<NSObject> JS::NativeNetworkingIOS::SpecSendRequestQuery::data() const
{
  id const p = _v[@"data"];
  return p;
}
inline id<NSObject> JS::NativeNetworkingIOS::SpecSendRequestQuery::headers() const
{
  id const p = _v[@"headers"];
  return p;
}
inline NSString *JS::NativeNetworkingIOS::SpecSendRequestQuery::responseType() const
{
  id const p = _v[@"responseType"];
  return RCTBridgingToString(p);
}
inline bool JS::NativeNetworkingIOS::SpecSendRequestQuery::incrementalUpdates() const
{
  id const p = _v[@"incrementalUpdates"];
  return RCTBridgingToBool(p);
}
inline double JS::NativeNetworkingIOS::SpecSendRequestQuery::timeout() const
{
  id const p = _v[@"timeout"];
  return RCTBridgingToDouble(p);
}
inline bool JS::NativeNetworkingIOS::SpecSendRequestQuery::withCredentials() const
{
  id const p = _v[@"withCredentials"];
  return RCTBridgingToBool(p);
}
inline JS::NativePlatformConstantsIOS::ConstantsReactNativeVersion::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto major = i.major.get();
  d[@"major"] = @(major);
  auto minor = i.minor.get();
  d[@"minor"] = @(minor);
  auto patch = i.patch.get();
  d[@"patch"] = @(patch);
  auto prerelease = i.prerelease.get();
  d[@"prerelease"] = prerelease;
  return d;
}) {}
inline JS::NativePlatformConstantsIOS::ConstantsReactNativeVersion::Builder::Builder(ConstantsReactNativeVersion i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativePlatformConstantsIOS::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto isTesting = i.isTesting.get();
  d[@"isTesting"] = @(isTesting);
  auto isDisableAnimations = i.isDisableAnimations;
  d[@"isDisableAnimations"] = isDisableAnimations.has_value() ? @((BOOL)isDisableAnimations.value()) : nil;
  auto reactNativeVersion = i.reactNativeVersion.get();
  d[@"reactNativeVersion"] = reactNativeVersion.buildUnsafeRawValue();
  auto forceTouchAvailable = i.forceTouchAvailable.get();
  d[@"forceTouchAvailable"] = @(forceTouchAvailable);
  auto osVersion = i.osVersion.get();
  d[@"osVersion"] = osVersion;
  auto systemName = i.systemName.get();
  d[@"systemName"] = systemName;
  auto interfaceIdiom = i.interfaceIdiom.get();
  d[@"interfaceIdiom"] = interfaceIdiom;
  auto isMacCatalyst = i.isMacCatalyst;
  d[@"isMacCatalyst"] = isMacCatalyst.has_value() ? @((BOOL)isMacCatalyst.value()) : nil;
  return d;
}) {}
inline JS::NativePlatformConstantsIOS::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline bool JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission::alert() const
{
  id const p = _v[@"alert"];
  return RCTBridgingToBool(p);
}
inline bool JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission::badge() const
{
  id const p = _v[@"badge"];
  return RCTBridgingToBool(p);
}
inline bool JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission::sound() const
{
  id const p = _v[@"sound"];
  return RCTBridgingToBool(p);
}
inline NSString *JS::NativePushNotificationManagerIOS::Notification::alertTitle() const
{
  id const p = _v[@"alertTitle"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativePushNotificationManagerIOS::Notification::alertBody() const
{
  id const p = _v[@"alertBody"];
  return RCTBridgingToOptionalString(p);
}
inline id<NSObject> _Nullable JS::NativePushNotificationManagerIOS::Notification::userInfo() const
{
  id const p = _v[@"userInfo"];
  return p;
}
inline NSString *JS::NativePushNotificationManagerIOS::Notification::category() const
{
  id const p = _v[@"category"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<double> JS::NativePushNotificationManagerIOS::Notification::fireDate() const
{
  id const p = _v[@"fireDate"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativePushNotificationManagerIOS::Notification::fireIntervalSeconds() const
{
  id const p = _v[@"fireIntervalSeconds"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativePushNotificationManagerIOS::Notification::applicationIconBadgeNumber() const
{
  id const p = _v[@"applicationIconBadgeNumber"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<bool> JS::NativePushNotificationManagerIOS::Notification::isSilent() const
{
  id const p = _v[@"isSilent"];
  return RCTBridgingToOptionalBool(p);
}
inline NSString *JS::NativePushNotificationManagerIOS::Notification::soundName() const
{
  id const p = _v[@"soundName"];
  return RCTBridgingToOptionalString(p);
}


inline JS::NativeSettingsManager::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto settings = i.settings.get();
  d[@"settings"] = settings;
  return d;
}) {}
inline JS::NativeSettingsManager::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline NSString *JS::NativeShareModule::SpecShareContent::title() const
{
  id const p = _v[@"title"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeShareModule::SpecShareContent::message() const
{
  id const p = _v[@"message"];
  return RCTBridgingToOptionalString(p);
}

inline JS::NativeSourceCode::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto scriptURL = i.scriptURL.get();
  d[@"scriptURL"] = scriptURL;
  return d;
}) {}
inline JS::NativeSourceCode::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeStatusBarManagerIOS::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto HEIGHT = i.HEIGHT.get();
  d[@"HEIGHT"] = @(HEIGHT);
  auto DEFAULT_BACKGROUND_COLOR = i.DEFAULT_BACKGROUND_COLOR;
  d[@"DEFAULT_BACKGROUND_COLOR"] = DEFAULT_BACKGROUND_COLOR.has_value() ? @((double)DEFAULT_BACKGROUND_COLOR.value()) : nil;
  return d;
}) {}
inline JS::NativeStatusBarManagerIOS::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}


inline id<NSObject> _Nullable JS::NativeWebSocketModule::SpecConnectOptions::headers() const
{
  id const p = _v[@"headers"];
  return p;
}
#endif // FBReactNativeSpec_H
