/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNCGeolocationSpec.h"

@implementation RCTCxxConvert (NativeRNCGeolocation_SpecSetConfigurationConfig)
+ (RCTManagedPointer *)JS_NativeRNCGeolocation_SpecSetConfigurationConfig:(id)json
{
  return facebook::react::managedPointer<JS::NativeRNCGeolocation::SpecSetConfigurationConfig>(json);
}
@end
@implementation RCTCxxConvert (NativeRNCGeolocation_GeolocationOptions)
+ (RCTManagedPointer *)JS_NativeRNCGeolocation_GeolocationOptions:(id)json
{
  return facebook::react::managedPointer<JS::NativeRNCGeolocation::GeolocationOptions>(json);
}
@end
namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_setConfiguration(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setConfiguration", @selector(setConfiguration:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_requestAuthorization(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "requestAuthorization", @selector(requestAuthorization:error:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_getCurrentPosition(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentPosition", @selector(getCurrentPosition:position:error:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_startObserving(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startObserving", @selector(startObserving:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_stopObserving(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stopObserving", @selector(stopObserving), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCGeolocationSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
    }

  NativeRNCGeolocationSpecJSI::NativeRNCGeolocationSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["setConfiguration"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationSpecJSI_setConfiguration};
        setMethodArgConversionSelector(@"setConfiguration", 0, @"JS_NativeRNCGeolocation_SpecSetConfigurationConfig:");
        
        methodMap_["requestAuthorization"] = MethodMetadata {2, __hostFunction_NativeRNCGeolocationSpecJSI_requestAuthorization};
        
        
        methodMap_["getCurrentPosition"] = MethodMetadata {3, __hostFunction_NativeRNCGeolocationSpecJSI_getCurrentPosition};
        setMethodArgConversionSelector(@"getCurrentPosition", 0, @"JS_NativeRNCGeolocation_GeolocationOptions:");
        
        methodMap_["startObserving"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationSpecJSI_startObserving};
        setMethodArgConversionSelector(@"startObserving", 0, @"JS_NativeRNCGeolocation_GeolocationOptions:");
        
        methodMap_["stopObserving"] = MethodMetadata {0, __hostFunction_NativeRNCGeolocationSpecJSI_stopObserving};
        
        
        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationSpecJSI_addListener};
        
        
        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationSpecJSI_removeListeners};
        
  }
} // namespace facebook::react
