/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of RNCGeolocationSpec symbols
#ifndef RNCGeolocationSpec_H
#define RNCGeolocationSpec_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>

namespace JS {
  namespace NativeRNCGeolocation {
    struct SpecSetConfigurationConfig {
      bool skipPermissionRequests() const;
      NSString *authorizationLevel() const;
      NSString *enableBackgroundLocationUpdates() const;

      SpecSetConfigurationConfig(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeRNCGeolocation_SpecSetConfigurationConfig)
+ (RCTManagedPointer *)JS_NativeRNCGeolocation_SpecSetConfigurationConfig:(id)json;
@end
namespace JS {
  namespace NativeRNCGeolocation {
    struct GeolocationOptions {
      std::optional<double> timeout() const;
      std::optional<double> maximumAge() const;
      std::optional<bool> enableHighAccuracy() const;
      std::optional<double> distanceFilter() const;
      std::optional<bool> useSignificantChanges() const;
      std::optional<double> interval() const;
      std::optional<double> fastestInterval() const;

      GeolocationOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeRNCGeolocation_GeolocationOptions)
+ (RCTManagedPointer *)JS_NativeRNCGeolocation_GeolocationOptions:(id)json;
@end
@protocol NativeRNCGeolocationSpec <RCTBridgeModule, RCTTurboModule>

- (void)setConfiguration:(JS::NativeRNCGeolocation::SpecSetConfigurationConfig &)config;
- (void)requestAuthorization:(RCTResponseSenderBlock)success
                       error:(RCTResponseSenderBlock)error;
- (void)getCurrentPosition:(JS::NativeRNCGeolocation::GeolocationOptions &)options
                  position:(RCTResponseSenderBlock)position
                     error:(RCTResponseSenderBlock)error;
- (void)startObserving:(JS::NativeRNCGeolocation::GeolocationOptions &)options;
- (void)stopObserving;
- (void)addListener:(NSString *)eventName;
- (void)removeListeners:(double)count;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeRNCGeolocation'
   */
  class JSI_EXPORT NativeRNCGeolocationSpecJSI : public ObjCTurboModule {
  public:
    NativeRNCGeolocationSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
inline bool JS::NativeRNCGeolocation::SpecSetConfigurationConfig::skipPermissionRequests() const
{
  id const p = _v[@"skipPermissionRequests"];
  return RCTBridgingToBool(p);
}
inline NSString *JS::NativeRNCGeolocation::SpecSetConfigurationConfig::authorizationLevel() const
{
  id const p = _v[@"authorizationLevel"];
  return RCTBridgingToOptionalString(p);
}
inline NSString *JS::NativeRNCGeolocation::SpecSetConfigurationConfig::enableBackgroundLocationUpdates() const
{
  id const p = _v[@"enableBackgroundLocationUpdates"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<double> JS::NativeRNCGeolocation::GeolocationOptions::timeout() const
{
  id const p = _v[@"timeout"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeRNCGeolocation::GeolocationOptions::maximumAge() const
{
  id const p = _v[@"maximumAge"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<bool> JS::NativeRNCGeolocation::GeolocationOptions::enableHighAccuracy() const
{
  id const p = _v[@"enableHighAccuracy"];
  return RCTBridgingToOptionalBool(p);
}
inline std::optional<double> JS::NativeRNCGeolocation::GeolocationOptions::distanceFilter() const
{
  id const p = _v[@"distanceFilter"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<bool> JS::NativeRNCGeolocation::GeolocationOptions::useSignificantChanges() const
{
  id const p = _v[@"useSignificantChanges"];
  return RCTBridgingToOptionalBool(p);
}
inline std::optional<double> JS::NativeRNCGeolocation::GeolocationOptions::interval() const
{
  id const p = _v[@"interval"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeRNCGeolocation::GeolocationOptions::fastestInterval() const
{
  id const p = _v[@"fastestInterval"];
  return RCTBridgingToOptionalDouble(p);
}
#endif // RNCGeolocationSpec_H
