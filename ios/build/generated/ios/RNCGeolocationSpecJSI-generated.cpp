/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNCGeolocationSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_setConfiguration(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->setConfiguration(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_requestAuthorization(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->requestAuthorization(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_getCurrentPosition(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->getCurrentPosition(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_startObserving(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->startObserving(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_stopObserving(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->stopObserving(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRNCGeolocationCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCGeolocationCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeRNCGeolocationCxxSpecJSI::NativeRNCGeolocationCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNCGeolocation", jsInvoker) {
  methodMap_["setConfiguration"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationCxxSpecJSI_setConfiguration};
  methodMap_["requestAuthorization"] = MethodMetadata {2, __hostFunction_NativeRNCGeolocationCxxSpecJSI_requestAuthorization};
  methodMap_["getCurrentPosition"] = MethodMetadata {3, __hostFunction_NativeRNCGeolocationCxxSpecJSI_getCurrentPosition};
  methodMap_["startObserving"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationCxxSpecJSI_startObserving};
  methodMap_["stopObserving"] = MethodMetadata {0, __hostFunction_NativeRNCGeolocationCxxSpecJSI_stopObserving};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeRNCGeolocationCxxSpecJSI_removeListeners};
}


} // namespace facebook::react
