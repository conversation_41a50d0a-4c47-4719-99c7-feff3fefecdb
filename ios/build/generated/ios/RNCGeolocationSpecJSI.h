/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  
#pragma mark - NativeRNCGeolocationGeolocationError

template <typename P0, typename P1, typename P2, typename P3, typename P4>
struct NativeRNCGeolocationGeolocationError {
  P0 code;
  P1 message;
  P2 PERMISSION_DENIED;
  P3 POSITION_UNAVAILABLE;
  P4 TIMEOUT;
  bool operator==(const NativeRNCGeolocationGeolocationError &other) const {
    return code == other.code && message == other.message && PERMISSION_DENIED == other.PERMISSION_DENIED && POSITION_UNAVAILABLE == other.POSITION_UNAVAILABLE && TIMEOUT == other.TIMEOUT;
  }
};

template <typename T>
struct NativeRNCGeolocationGeolocationErrorBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.code)>(rt, value.getProperty(rt, "code"), jsInvoker),
      bridging::fromJs<decltype(types.message)>(rt, value.getProperty(rt, "message"), jsInvoker),
      bridging::fromJs<decltype(types.PERMISSION_DENIED)>(rt, value.getProperty(rt, "PERMISSION_DENIED"), jsInvoker),
      bridging::fromJs<decltype(types.POSITION_UNAVAILABLE)>(rt, value.getProperty(rt, "POSITION_UNAVAILABLE"), jsInvoker),
      bridging::fromJs<decltype(types.TIMEOUT)>(rt, value.getProperty(rt, "TIMEOUT"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double codeToJs(jsi::Runtime &rt, decltype(types.code) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String messageToJs(jsi::Runtime &rt, decltype(types.message) value) {
    return bridging::toJs(rt, value);
  }

  static double PERMISSION_DENIEDToJs(jsi::Runtime &rt, decltype(types.PERMISSION_DENIED) value) {
    return bridging::toJs(rt, value);
  }

  static double POSITION_UNAVAILABLEToJs(jsi::Runtime &rt, decltype(types.POSITION_UNAVAILABLE) value) {
    return bridging::toJs(rt, value);
  }

  static double TIMEOUTToJs(jsi::Runtime &rt, decltype(types.TIMEOUT) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "code", bridging::toJs(rt, value.code, jsInvoker));
    result.setProperty(rt, "message", bridging::toJs(rt, value.message, jsInvoker));
    result.setProperty(rt, "PERMISSION_DENIED", bridging::toJs(rt, value.PERMISSION_DENIED, jsInvoker));
    result.setProperty(rt, "POSITION_UNAVAILABLE", bridging::toJs(rt, value.POSITION_UNAVAILABLE, jsInvoker));
    result.setProperty(rt, "TIMEOUT", bridging::toJs(rt, value.TIMEOUT, jsInvoker));
    return result;
  }
};



#pragma mark - NativeRNCGeolocationGeolocationOptions

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6>
struct NativeRNCGeolocationGeolocationOptions {
  P0 timeout;
  P1 maximumAge;
  P2 enableHighAccuracy;
  P3 distanceFilter;
  P4 useSignificantChanges;
  P5 interval;
  P6 fastestInterval;
  bool operator==(const NativeRNCGeolocationGeolocationOptions &other) const {
    return timeout == other.timeout && maximumAge == other.maximumAge && enableHighAccuracy == other.enableHighAccuracy && distanceFilter == other.distanceFilter && useSignificantChanges == other.useSignificantChanges && interval == other.interval && fastestInterval == other.fastestInterval;
  }
};

template <typename T>
struct NativeRNCGeolocationGeolocationOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.timeout)>(rt, value.getProperty(rt, "timeout"), jsInvoker),
      bridging::fromJs<decltype(types.maximumAge)>(rt, value.getProperty(rt, "maximumAge"), jsInvoker),
      bridging::fromJs<decltype(types.enableHighAccuracy)>(rt, value.getProperty(rt, "enableHighAccuracy"), jsInvoker),
      bridging::fromJs<decltype(types.distanceFilter)>(rt, value.getProperty(rt, "distanceFilter"), jsInvoker),
      bridging::fromJs<decltype(types.useSignificantChanges)>(rt, value.getProperty(rt, "useSignificantChanges"), jsInvoker),
      bridging::fromJs<decltype(types.interval)>(rt, value.getProperty(rt, "interval"), jsInvoker),
      bridging::fromJs<decltype(types.fastestInterval)>(rt, value.getProperty(rt, "fastestInterval"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double timeoutToJs(jsi::Runtime &rt, decltype(types.timeout) value) {
    return bridging::toJs(rt, value);
  }

  static double maximumAgeToJs(jsi::Runtime &rt, decltype(types.maximumAge) value) {
    return bridging::toJs(rt, value);
  }

  static bool enableHighAccuracyToJs(jsi::Runtime &rt, decltype(types.enableHighAccuracy) value) {
    return bridging::toJs(rt, value);
  }

  static double distanceFilterToJs(jsi::Runtime &rt, decltype(types.distanceFilter) value) {
    return bridging::toJs(rt, value);
  }

  static bool useSignificantChangesToJs(jsi::Runtime &rt, decltype(types.useSignificantChanges) value) {
    return bridging::toJs(rt, value);
  }

  static double intervalToJs(jsi::Runtime &rt, decltype(types.interval) value) {
    return bridging::toJs(rt, value);
  }

  static double fastestIntervalToJs(jsi::Runtime &rt, decltype(types.fastestInterval) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.timeout) {
      result.setProperty(rt, "timeout", bridging::toJs(rt, value.timeout.value(), jsInvoker));
    }
    if (value.maximumAge) {
      result.setProperty(rt, "maximumAge", bridging::toJs(rt, value.maximumAge.value(), jsInvoker));
    }
    if (value.enableHighAccuracy) {
      result.setProperty(rt, "enableHighAccuracy", bridging::toJs(rt, value.enableHighAccuracy.value(), jsInvoker));
    }
    if (value.distanceFilter) {
      result.setProperty(rt, "distanceFilter", bridging::toJs(rt, value.distanceFilter.value(), jsInvoker));
    }
    if (value.useSignificantChanges) {
      result.setProperty(rt, "useSignificantChanges", bridging::toJs(rt, value.useSignificantChanges.value(), jsInvoker));
    }
    if (value.interval) {
      result.setProperty(rt, "interval", bridging::toJs(rt, value.interval.value(), jsInvoker));
    }
    if (value.fastestInterval) {
      result.setProperty(rt, "fastestInterval", bridging::toJs(rt, value.fastestInterval.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeRNCGeolocationGeolocationResponse

template <typename P0, typename P1>
struct NativeRNCGeolocationGeolocationResponse {
  P0 coords;
  P1 timestamp;
  bool operator==(const NativeRNCGeolocationGeolocationResponse &other) const {
    return coords == other.coords && timestamp == other.timestamp;
  }
};

template <typename T>
struct NativeRNCGeolocationGeolocationResponseBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.coords)>(rt, value.getProperty(rt, "coords"), jsInvoker),
      bridging::fromJs<decltype(types.timestamp)>(rt, value.getProperty(rt, "timestamp"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Object coordsToJs(jsi::Runtime &rt, decltype(types.coords) value) {
    return bridging::toJs(rt, value);
  }

  static double timestampToJs(jsi::Runtime &rt, decltype(types.timestamp) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "coords", bridging::toJs(rt, value.coords, jsInvoker));
    result.setProperty(rt, "timestamp", bridging::toJs(rt, value.timestamp, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeRNCGeolocationCxxSpecJSI : public TurboModule {
protected:
  NativeRNCGeolocationCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void setConfiguration(jsi::Runtime &rt, jsi::Object config) = 0;
  virtual void requestAuthorization(jsi::Runtime &rt, jsi::Function success, jsi::Function error) = 0;
  virtual void getCurrentPosition(jsi::Runtime &rt, jsi::Object options, jsi::Function position, jsi::Function error) = 0;
  virtual void startObserving(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual void stopObserving(jsi::Runtime &rt) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeRNCGeolocationCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNCGeolocation";

protected:
  NativeRNCGeolocationCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRNCGeolocationCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRNCGeolocationCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRNCGeolocationCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void setConfiguration(jsi::Runtime &rt, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::setConfiguration) == 2,
          "Expected setConfiguration(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setConfiguration, jsInvoker_, instance_, std::move(config));
    }
    void requestAuthorization(jsi::Runtime &rt, jsi::Function success, jsi::Function error) override {
      static_assert(
          bridging::getParameterCount(&T::requestAuthorization) == 3,
          "Expected requestAuthorization(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::requestAuthorization, jsInvoker_, instance_, std::move(success), std::move(error));
    }
    void getCurrentPosition(jsi::Runtime &rt, jsi::Object options, jsi::Function position, jsi::Function error) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentPosition) == 4,
          "Expected getCurrentPosition(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentPosition, jsInvoker_, instance_, std::move(options), std::move(position), std::move(error));
    }
    void startObserving(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::startObserving) == 2,
          "Expected startObserving(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startObserving, jsInvoker_, instance_, std::move(options));
    }
    void stopObserving(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::stopObserving) == 1,
          "Expected stopObserving(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::stopObserving, jsInvoker_, instance_);
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeRNCGeolocationCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
