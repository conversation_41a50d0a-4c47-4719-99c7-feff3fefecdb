/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNCompressorSpec.h"


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_image_compress(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "image_compress", @selector(image_compress:optionMap:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_getImageMetaData(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getImageMetaData", @selector(getImageMetaData:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_compress(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "compress", @selector(compress:optionMap:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_cancelCompression(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cancelCompression", @selector(cancelCompression:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_getVideoMetaData(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getVideoMetaData", @selector(getVideoMetaData:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_activateBackgroundTask(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "activateBackgroundTask", @selector(activateBackgroundTask:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_deactivateBackgroundTask(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "deactivateBackgroundTask", @selector(deactivateBackgroundTask:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_compress_audio(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "compress_audio", @selector(compress_audio:optionMap:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_upload(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "upload", @selector(upload:options:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_cancelUpload(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cancelUpload", @selector(cancelUpload:shouldCancelAll:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_download(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "download", @selector(download:options:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_generateFilePath(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "generateFilePath", @selector(generateFilePath:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_getRealPath(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getRealPath", @selector(getRealPath:type:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_getFileSize(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getFileSize", @selector(getFileSize:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_createVideoThumbnail(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "createVideoThumbnail", @selector(createVideoThumbnail:options:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeCompressorSpecJSI_clearCache(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "clearCache", @selector(clearCache:resolve:reject:), args, count);
    }

  NativeCompressorSpecJSI::NativeCompressorSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["image_compress"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_image_compress};
        
        
        methodMap_["getImageMetaData"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_getImageMetaData};
        
        
        methodMap_["compress"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_compress};
        
        
        methodMap_["cancelCompression"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_cancelCompression};
        
        
        methodMap_["getVideoMetaData"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_getVideoMetaData};
        
        
        methodMap_["activateBackgroundTask"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_activateBackgroundTask};
        
        
        methodMap_["deactivateBackgroundTask"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_deactivateBackgroundTask};
        
        
        methodMap_["compress_audio"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_compress_audio};
        
        
        methodMap_["upload"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_upload};
        
        
        methodMap_["cancelUpload"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_cancelUpload};
        
        
        methodMap_["download"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_download};
        
        
        methodMap_["generateFilePath"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_generateFilePath};
        
        
        methodMap_["getRealPath"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_getRealPath};
        
        
        methodMap_["getFileSize"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_getFileSize};
        
        
        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_addListener};
        
        
        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_removeListeners};
        
        
        methodMap_["createVideoThumbnail"] = MethodMetadata {2, __hostFunction_NativeCompressorSpecJSI_createVideoThumbnail};
        
        
        methodMap_["clearCache"] = MethodMetadata {1, __hostFunction_NativeCompressorSpecJSI_clearCache};
        
  }
} // namespace facebook::react
