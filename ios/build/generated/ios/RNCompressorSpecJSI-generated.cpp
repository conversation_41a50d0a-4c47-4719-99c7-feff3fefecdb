/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNCompressorSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_image_compress(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->image_compress(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_getImageMetaData(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->getImageMetaData(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_compress(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->compress(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_cancelCompression(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->cancelCompression(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_getVideoMetaData(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->getVideoMetaData(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_activateBackgroundTask(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->activateBackgroundTask(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_deactivateBackgroundTask(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->deactivateBackgroundTask(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_compress_audio(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->compress_audio(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_upload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->upload(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_cancelUpload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->cancelUpload(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_download(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->download(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_generateFilePath(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->generateFilePath(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_getRealPath(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->getRealPath(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_getFileSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->getFileSize(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_createVideoThumbnail(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->createVideoThumbnail(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeCompressorCxxSpecJSI_clearCache(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCompressorCxxSpecJSI *>(&turboModule)->clearCache(
    rt,
    count <= 0 || args[0].isNull() || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt))
  );
}

NativeCompressorCxxSpecJSI::NativeCompressorCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Compressor", jsInvoker) {
  methodMap_["image_compress"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_image_compress};
  methodMap_["getImageMetaData"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_getImageMetaData};
  methodMap_["compress"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_compress};
  methodMap_["cancelCompression"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_cancelCompression};
  methodMap_["getVideoMetaData"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_getVideoMetaData};
  methodMap_["activateBackgroundTask"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_activateBackgroundTask};
  methodMap_["deactivateBackgroundTask"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_deactivateBackgroundTask};
  methodMap_["compress_audio"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_compress_audio};
  methodMap_["upload"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_upload};
  methodMap_["cancelUpload"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_cancelUpload};
  methodMap_["download"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_download};
  methodMap_["generateFilePath"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_generateFilePath};
  methodMap_["getRealPath"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_getRealPath};
  methodMap_["getFileSize"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_getFileSize};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_removeListeners};
  methodMap_["createVideoThumbnail"] = MethodMetadata {2, __hostFunction_NativeCompressorCxxSpecJSI_createVideoThumbnail};
  methodMap_["clearCache"] = MethodMetadata {1, __hostFunction_NativeCompressorCxxSpecJSI_clearCache};
}


} // namespace facebook::react
