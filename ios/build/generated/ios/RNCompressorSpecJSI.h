/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeCompressorCxxSpecJSI : public TurboModule {
protected:
  NativeCompressorCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value image_compress(jsi::Runtime &rt, jsi::String imagePath, jsi::Object optionMap) = 0;
  virtual jsi::Value getImageMetaData(jsi::Runtime &rt, jsi::String filePath) = 0;
  virtual jsi::Value compress(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object optionMap) = 0;
  virtual void cancelCompression(jsi::Runtime &rt, jsi::String uuid) = 0;
  virtual jsi::Value getVideoMetaData(jsi::Runtime &rt, jsi::String filePath) = 0;
  virtual jsi::Value activateBackgroundTask(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual jsi::Value deactivateBackgroundTask(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual jsi::Value compress_audio(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object optionMap) = 0;
  virtual jsi::Value upload(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object options) = 0;
  virtual void cancelUpload(jsi::Runtime &rt, jsi::String uuid, bool shouldCancelAll) = 0;
  virtual jsi::Value download(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object options) = 0;
  virtual jsi::Value generateFilePath(jsi::Runtime &rt, jsi::String _extension) = 0;
  virtual jsi::Value getRealPath(jsi::Runtime &rt, jsi::String path, jsi::String type) = 0;
  virtual jsi::Value getFileSize(jsi::Runtime &rt, jsi::String filePath) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;
  virtual jsi::Value createVideoThumbnail(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object options) = 0;
  virtual jsi::Value clearCache(jsi::Runtime &rt, std::optional<jsi::String> cacheDir) = 0;

};

template <typename T>
class JSI_EXPORT NativeCompressorCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "Compressor";

protected:
  NativeCompressorCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeCompressorCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeCompressorCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeCompressorCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value image_compress(jsi::Runtime &rt, jsi::String imagePath, jsi::Object optionMap) override {
      static_assert(
          bridging::getParameterCount(&T::image_compress) == 3,
          "Expected image_compress(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::image_compress, jsInvoker_, instance_, std::move(imagePath), std::move(optionMap));
    }
    jsi::Value getImageMetaData(jsi::Runtime &rt, jsi::String filePath) override {
      static_assert(
          bridging::getParameterCount(&T::getImageMetaData) == 2,
          "Expected getImageMetaData(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getImageMetaData, jsInvoker_, instance_, std::move(filePath));
    }
    jsi::Value compress(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object optionMap) override {
      static_assert(
          bridging::getParameterCount(&T::compress) == 3,
          "Expected compress(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::compress, jsInvoker_, instance_, std::move(fileUrl), std::move(optionMap));
    }
    void cancelCompression(jsi::Runtime &rt, jsi::String uuid) override {
      static_assert(
          bridging::getParameterCount(&T::cancelCompression) == 2,
          "Expected cancelCompression(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cancelCompression, jsInvoker_, instance_, std::move(uuid));
    }
    jsi::Value getVideoMetaData(jsi::Runtime &rt, jsi::String filePath) override {
      static_assert(
          bridging::getParameterCount(&T::getVideoMetaData) == 2,
          "Expected getVideoMetaData(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getVideoMetaData, jsInvoker_, instance_, std::move(filePath));
    }
    jsi::Value activateBackgroundTask(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::activateBackgroundTask) == 2,
          "Expected activateBackgroundTask(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::activateBackgroundTask, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value deactivateBackgroundTask(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::deactivateBackgroundTask) == 2,
          "Expected deactivateBackgroundTask(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::deactivateBackgroundTask, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value compress_audio(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object optionMap) override {
      static_assert(
          bridging::getParameterCount(&T::compress_audio) == 3,
          "Expected compress_audio(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::compress_audio, jsInvoker_, instance_, std::move(fileUrl), std::move(optionMap));
    }
    jsi::Value upload(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::upload) == 3,
          "Expected upload(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::upload, jsInvoker_, instance_, std::move(fileUrl), std::move(options));
    }
    void cancelUpload(jsi::Runtime &rt, jsi::String uuid, bool shouldCancelAll) override {
      static_assert(
          bridging::getParameterCount(&T::cancelUpload) == 3,
          "Expected cancelUpload(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cancelUpload, jsInvoker_, instance_, std::move(uuid), std::move(shouldCancelAll));
    }
    jsi::Value download(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::download) == 3,
          "Expected download(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::download, jsInvoker_, instance_, std::move(fileUrl), std::move(options));
    }
    jsi::Value generateFilePath(jsi::Runtime &rt, jsi::String _extension) override {
      static_assert(
          bridging::getParameterCount(&T::generateFilePath) == 2,
          "Expected generateFilePath(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::generateFilePath, jsInvoker_, instance_, std::move(_extension));
    }
    jsi::Value getRealPath(jsi::Runtime &rt, jsi::String path, jsi::String type) override {
      static_assert(
          bridging::getParameterCount(&T::getRealPath) == 3,
          "Expected getRealPath(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getRealPath, jsInvoker_, instance_, std::move(path), std::move(type));
    }
    jsi::Value getFileSize(jsi::Runtime &rt, jsi::String filePath) override {
      static_assert(
          bridging::getParameterCount(&T::getFileSize) == 2,
          "Expected getFileSize(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getFileSize, jsInvoker_, instance_, std::move(filePath));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }
    jsi::Value createVideoThumbnail(jsi::Runtime &rt, jsi::String fileUrl, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::createVideoThumbnail) == 3,
          "Expected createVideoThumbnail(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::createVideoThumbnail, jsInvoker_, instance_, std::move(fileUrl), std::move(options));
    }
    jsi::Value clearCache(jsi::Runtime &rt, std::optional<jsi::String> cacheDir) override {
      static_assert(
          bridging::getParameterCount(&T::clearCache) == 2,
          "Expected clearCache(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::clearCache, jsInvoker_, instance_, std::move(cacheDir));
    }

  private:
    friend class NativeCompressorCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
