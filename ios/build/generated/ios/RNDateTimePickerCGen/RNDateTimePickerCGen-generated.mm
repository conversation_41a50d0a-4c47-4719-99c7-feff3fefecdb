/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNDateTimePickerCGen.h"

@implementation RCTCxxConvert (NativeModuleDatePicker_SpecOpenParamsDialogButtons)
+ (RCTManagedPointer *)JS_NativeModuleDatePicker_SpecOpenParamsDialogButtons:(id)json
{
  return facebook::react::managedPointer<JS::NativeModuleDatePicker::SpecOpenParamsDialogButtons>(json);
}
@end
@implementation RCTCxxConvert (NativeModuleDatePicker_SpecOpenParams)
+ (RCTManagedPointer *)JS_NativeModuleDatePicker_SpecOpenParams:(id)json
{
  return facebook::react::managedPointer<JS::NativeModuleDatePicker::SpecOpenParams>(json);
}
@end
namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeModuleDatePickerSpecJSI_dismiss(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "dismiss", @selector(dismiss:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeModuleDatePickerSpecJSI_open(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "open", @selector(open:resolve:reject:), args, count);
    }

  NativeModuleDatePickerSpecJSI::NativeModuleDatePickerSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["dismiss"] = MethodMetadata {0, __hostFunction_NativeModuleDatePickerSpecJSI_dismiss};
        
        
        methodMap_["open"] = MethodMetadata {1, __hostFunction_NativeModuleDatePickerSpecJSI_open};
        setMethodArgConversionSelector(@"open", 0, @"JS_NativeModuleDatePicker_SpecOpenParams:");
  }
} // namespace facebook::react
@implementation RCTCxxConvert (NativeModuleTimePicker_SpecOpenParamsDialogButtons)
+ (RCTManagedPointer *)JS_NativeModuleTimePicker_SpecOpenParamsDialogButtons:(id)json
{
  return facebook::react::managedPointer<JS::NativeModuleTimePicker::SpecOpenParamsDialogButtons>(json);
}
@end
@implementation RCTCxxConvert (NativeModuleTimePicker_SpecOpenParams)
+ (RCTManagedPointer *)JS_NativeModuleTimePicker_SpecOpenParams:(id)json
{
  return facebook::react::managedPointer<JS::NativeModuleTimePicker::SpecOpenParams>(json);
}
@end
namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeModuleTimePickerSpecJSI_dismiss(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "dismiss", @selector(dismiss:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeModuleTimePickerSpecJSI_open(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "open", @selector(open:resolve:reject:), args, count);
    }

  NativeModuleTimePickerSpecJSI::NativeModuleTimePickerSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["dismiss"] = MethodMetadata {0, __hostFunction_NativeModuleTimePickerSpecJSI_dismiss};
        
        
        methodMap_["open"] = MethodMetadata {1, __hostFunction_NativeModuleTimePickerSpecJSI_open};
        setMethodArgConversionSelector(@"open", 0, @"JS_NativeModuleTimePicker_SpecOpenParams:");
  }
} // namespace facebook::react
