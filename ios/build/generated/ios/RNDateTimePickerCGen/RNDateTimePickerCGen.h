/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of RNDateTimePickerCGen symbols
#ifndef RNDateTimePickerCGen_H
#define RNDateTimePickerCGen_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>

namespace JS {
  namespace NativeModuleDatePicker {
    struct SpecOpenParamsDialogButtons {
      NSString *string() const;

      SpecOpenParamsDialogButtons(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeModuleDatePicker_SpecOpenParamsDialogButtons)
+ (RCTManagedPointer *)JS_NativeModuleDatePicker_SpecOpenParamsDialogButtons:(id)json;
@end
namespace JS {
  namespace NativeModuleDatePicker {
    struct SpecOpenParams {
      std::optional<JS::NativeModuleDatePicker::SpecOpenParamsDialogButtons> dialogButtons() const;
      NSString *display() const;
      std::optional<double> maximumDate() const;
      std::optional<double> minimumDate() const;
      NSString *testID() const;
      std::optional<double> timeZoneName() const;
      std::optional<double> timeZoneOffsetInMinutes() const;

      SpecOpenParams(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeModuleDatePicker_SpecOpenParams)
+ (RCTManagedPointer *)JS_NativeModuleDatePicker_SpecOpenParams:(id)json;
@end
@protocol NativeModuleDatePickerSpec <RCTBridgeModule, RCTTurboModule>

- (void)dismiss:(RCTPromiseResolveBlock)resolve
         reject:(RCTPromiseRejectBlock)reject;
- (void)open:(JS::NativeModuleDatePicker::SpecOpenParams &)params
     resolve:(RCTPromiseResolveBlock)resolve
      reject:(RCTPromiseRejectBlock)reject;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeModuleDatePicker'
   */
  class JSI_EXPORT NativeModuleDatePickerSpecJSI : public ObjCTurboModule {
  public:
    NativeModuleDatePickerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
namespace JS {
  namespace NativeModuleTimePicker {
    struct SpecOpenParamsDialogButtons {
      NSString *string() const;

      SpecOpenParamsDialogButtons(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeModuleTimePicker_SpecOpenParamsDialogButtons)
+ (RCTManagedPointer *)JS_NativeModuleTimePicker_SpecOpenParamsDialogButtons:(id)json;
@end
namespace JS {
  namespace NativeModuleTimePicker {
    struct SpecOpenParams {
      std::optional<JS::NativeModuleTimePicker::SpecOpenParamsDialogButtons> dialogButtons() const;
      NSString *display() const;
      std::optional<bool> is24Hour() const;
      std::optional<double> minuteInterval() const;
      std::optional<double> timeZoneOffsetInMinutes() const;

      SpecOpenParams(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeModuleTimePicker_SpecOpenParams)
+ (RCTManagedPointer *)JS_NativeModuleTimePicker_SpecOpenParams:(id)json;
@end
@protocol NativeModuleTimePickerSpec <RCTBridgeModule, RCTTurboModule>

- (void)dismiss:(RCTPromiseResolveBlock)resolve
         reject:(RCTPromiseRejectBlock)reject;
- (void)open:(JS::NativeModuleTimePicker::SpecOpenParams &)params
     resolve:(RCTPromiseResolveBlock)resolve
      reject:(RCTPromiseRejectBlock)reject;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeModuleTimePicker'
   */
  class JSI_EXPORT NativeModuleTimePickerSpecJSI : public ObjCTurboModule {
  public:
    NativeModuleTimePickerSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
inline NSString *JS::NativeModuleDatePicker::SpecOpenParamsDialogButtons::string() const
{
  id const p = _v[@"string"];
  return RCTBridgingToString(p);
}
inline std::optional<JS::NativeModuleDatePicker::SpecOpenParamsDialogButtons> JS::NativeModuleDatePicker::SpecOpenParams::dialogButtons() const
{
  id const p = _v[@"dialogButtons"];
  return (p == nil ? std::nullopt : std::make_optional(JS::NativeModuleDatePicker::SpecOpenParamsDialogButtons(p)));
}
inline NSString *JS::NativeModuleDatePicker::SpecOpenParams::display() const
{
  id const p = _v[@"display"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<double> JS::NativeModuleDatePicker::SpecOpenParams::maximumDate() const
{
  id const p = _v[@"maximumDate"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeModuleDatePicker::SpecOpenParams::minimumDate() const
{
  id const p = _v[@"minimumDate"];
  return RCTBridgingToOptionalDouble(p);
}
inline NSString *JS::NativeModuleDatePicker::SpecOpenParams::testID() const
{
  id const p = _v[@"testID"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<double> JS::NativeModuleDatePicker::SpecOpenParams::timeZoneName() const
{
  id const p = _v[@"timeZoneName"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeModuleDatePicker::SpecOpenParams::timeZoneOffsetInMinutes() const
{
  id const p = _v[@"timeZoneOffsetInMinutes"];
  return RCTBridgingToOptionalDouble(p);
}
inline NSString *JS::NativeModuleTimePicker::SpecOpenParamsDialogButtons::string() const
{
  id const p = _v[@"string"];
  return RCTBridgingToString(p);
}
inline std::optional<JS::NativeModuleTimePicker::SpecOpenParamsDialogButtons> JS::NativeModuleTimePicker::SpecOpenParams::dialogButtons() const
{
  id const p = _v[@"dialogButtons"];
  return (p == nil ? std::nullopt : std::make_optional(JS::NativeModuleTimePicker::SpecOpenParamsDialogButtons(p)));
}
inline NSString *JS::NativeModuleTimePicker::SpecOpenParams::display() const
{
  id const p = _v[@"display"];
  return RCTBridgingToOptionalString(p);
}
inline std::optional<bool> JS::NativeModuleTimePicker::SpecOpenParams::is24Hour() const
{
  id const p = _v[@"is24Hour"];
  return RCTBridgingToOptionalBool(p);
}
inline std::optional<double> JS::NativeModuleTimePicker::SpecOpenParams::minuteInterval() const
{
  id const p = _v[@"minuteInterval"];
  return RCTBridgingToOptionalDouble(p);
}
inline std::optional<double> JS::NativeModuleTimePicker::SpecOpenParams::timeZoneOffsetInMinutes() const
{
  id const p = _v[@"timeZoneOffsetInMinutes"];
  return RCTBridgingToOptionalDouble(p);
}
#endif // RNDateTimePickerCGen_H
