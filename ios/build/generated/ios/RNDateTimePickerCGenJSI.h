/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeModuleDatePickerCxxSpecJSI : public TurboModule {
protected:
  NativeModuleDatePickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value dismiss(jsi::Runtime &rt) = 0;
  virtual jsi::Value open(jsi::Runtime &rt, jsi::Object params) = 0;

};

template <typename T>
class JSI_EXPORT NativeModuleDatePickerCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNCDatePicker";

protected:
  NativeModuleDatePickerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeModuleDatePickerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeModuleDatePickerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeModuleDatePickerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value dismiss(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::dismiss) == 1,
          "Expected dismiss(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::dismiss, jsInvoker_, instance_);
    }
    jsi::Value open(jsi::Runtime &rt, jsi::Object params) override {
      static_assert(
          bridging::getParameterCount(&T::open) == 2,
          "Expected open(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::open, jsInvoker_, instance_, std::move(params));
    }

  private:
    friend class NativeModuleDatePickerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeModuleTimePickerCxxSpecJSI : public TurboModule {
protected:
  NativeModuleTimePickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value dismiss(jsi::Runtime &rt) = 0;
  virtual jsi::Value open(jsi::Runtime &rt, jsi::Object params) = 0;

};

template <typename T>
class JSI_EXPORT NativeModuleTimePickerCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNCTimePicker";

protected:
  NativeModuleTimePickerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeModuleTimePickerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeModuleTimePickerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeModuleTimePickerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value dismiss(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::dismiss) == 1,
          "Expected dismiss(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::dismiss, jsInvoker_, instance_);
    }
    jsi::Value open(jsi::Runtime &rt, jsi::Object params) override {
      static_assert(
          bridging::getParameterCount(&T::open) == 2,
          "Expected open(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::open, jsInvoker_, instance_, std::move(params));
    }

  private:
    friend class NativeModuleTimePickerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
