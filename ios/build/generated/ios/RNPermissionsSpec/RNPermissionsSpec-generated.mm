/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNPermissionsSpec.h"


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_openSettings(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "openSettings", @selector(openSettings:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_check(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "check", @selector(check:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_checkNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "checkNotifications", @selector(checkNotifications:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_request(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "request", @selector(request:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_requestNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "requestNotifications", @selector(requestNotifications:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_checkMultiple(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "checkMultiple", @selector(checkMultiple:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_requestMultiple(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "requestMultiple", @selector(requestMultiple:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_shouldShowRequestRationale(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "shouldShowRequestRationale", @selector(shouldShowRequestRationale:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_checkLocationAccuracy(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "checkLocationAccuracy", @selector(checkLocationAccuracy:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_openPhotoPicker(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "openPhotoPicker", @selector(openPhotoPicker:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_requestLocationAccuracy(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "requestLocationAccuracy", @selector(requestLocationAccuracy:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNPermissionsSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
    }

  NativeRNPermissionsSpecJSI::NativeRNPermissionsSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsSpecJSI_openSettings};
        
        
        methodMap_["check"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_check};
        
        
        methodMap_["checkNotifications"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsSpecJSI_checkNotifications};
        
        
        methodMap_["request"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_request};
        
        
        methodMap_["requestNotifications"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_requestNotifications};
        
        
        methodMap_["checkMultiple"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_checkMultiple};
        
        
        methodMap_["requestMultiple"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_requestMultiple};
        
        
        methodMap_["shouldShowRequestRationale"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_shouldShowRequestRationale};
        
        
        methodMap_["checkLocationAccuracy"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsSpecJSI_checkLocationAccuracy};
        
        
        methodMap_["openPhotoPicker"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsSpecJSI_openPhotoPicker};
        
        
        methodMap_["requestLocationAccuracy"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsSpecJSI_requestLocationAccuracy};
        
        
        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsSpecJSI_getConstants};
        
  }
} // namespace facebook::react
