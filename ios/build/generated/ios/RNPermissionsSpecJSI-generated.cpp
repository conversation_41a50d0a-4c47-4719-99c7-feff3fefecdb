/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNPermissionsSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_openSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->openSettings(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_check(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->check(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_checkNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->checkNotifications(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_request(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->request(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_requestNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->requestNotifications(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_checkMultiple(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->checkMultiple(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_requestMultiple(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->requestMultiple(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_shouldShowRequestRationale(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->shouldShowRequestRationale(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_checkLocationAccuracy(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->checkLocationAccuracy(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_openPhotoPicker(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->openPhotoPicker(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNPermissionsCxxSpecJSI_requestLocationAccuracy(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNPermissionsCxxSpecJSI *>(&turboModule)->requestLocationAccuracy(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}

NativeRNPermissionsCxxSpecJSI::NativeRNPermissionsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNPermissions", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsCxxSpecJSI_getConstants};
  methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsCxxSpecJSI_openSettings};
  methodMap_["check"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_check};
  methodMap_["checkNotifications"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsCxxSpecJSI_checkNotifications};
  methodMap_["request"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_request};
  methodMap_["requestNotifications"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_requestNotifications};
  methodMap_["checkMultiple"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_checkMultiple};
  methodMap_["requestMultiple"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_requestMultiple};
  methodMap_["shouldShowRequestRationale"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_shouldShowRequestRationale};
  methodMap_["checkLocationAccuracy"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsCxxSpecJSI_checkLocationAccuracy};
  methodMap_["openPhotoPicker"] = MethodMetadata {0, __hostFunction_NativeRNPermissionsCxxSpecJSI_openPhotoPicker};
  methodMap_["requestLocationAccuracy"] = MethodMetadata {1, __hostFunction_NativeRNPermissionsCxxSpecJSI_requestLocationAccuracy};
}


} // namespace facebook::react
