/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  
#pragma mark - NativeRNPermissionsNotificationsResponse

template <typename P0, typename P1>
struct NativeRNPermissionsNotificationsResponse {
  P0 status;
  P1 settings;
  bool operator==(const NativeRNPermissionsNotificationsResponse &other) const {
    return status == other.status && settings == other.settings;
  }
};

template <typename T>
struct NativeRNPermissionsNotificationsResponseBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.status)>(rt, value.getProperty(rt, "status"), jsInvoker),
      bridging::fromJs<decltype(types.settings)>(rt, value.getProperty(rt, "settings"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Object statusToJs(jsi::Runtime &rt, decltype(types.status) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object settingsToJs(jsi::Runtime &rt, decltype(types.settings) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "status", bridging::toJs(rt, value.status, jsInvoker));
    result.setProperty(rt, "settings", bridging::toJs(rt, value.settings, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeRNPermissionsCxxSpecJSI : public TurboModule {
protected:
  NativeRNPermissionsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value openSettings(jsi::Runtime &rt) = 0;
  virtual jsi::Value check(jsi::Runtime &rt, jsi::String permission) = 0;
  virtual jsi::Value checkNotifications(jsi::Runtime &rt) = 0;
  virtual jsi::Value request(jsi::Runtime &rt, jsi::String permission) = 0;
  virtual jsi::Value requestNotifications(jsi::Runtime &rt, jsi::Array options) = 0;
  virtual jsi::Value checkMultiple(jsi::Runtime &rt, jsi::Array permissions) = 0;
  virtual jsi::Value requestMultiple(jsi::Runtime &rt, jsi::Array permissions) = 0;
  virtual jsi::Value shouldShowRequestRationale(jsi::Runtime &rt, jsi::String permission) = 0;
  virtual jsi::Value checkLocationAccuracy(jsi::Runtime &rt) = 0;
  virtual jsi::Value openPhotoPicker(jsi::Runtime &rt) = 0;
  virtual jsi::Value requestLocationAccuracy(jsi::Runtime &rt, jsi::String purposeKey) = 0;

};

template <typename T>
class JSI_EXPORT NativeRNPermissionsCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNPermissions";

protected:
  NativeRNPermissionsCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRNPermissionsCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRNPermissionsCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRNPermissionsCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value openSettings(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::openSettings) == 1,
          "Expected openSettings(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openSettings, jsInvoker_, instance_);
    }
    jsi::Value check(jsi::Runtime &rt, jsi::String permission) override {
      static_assert(
          bridging::getParameterCount(&T::check) == 2,
          "Expected check(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::check, jsInvoker_, instance_, std::move(permission));
    }
    jsi::Value checkNotifications(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::checkNotifications) == 1,
          "Expected checkNotifications(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::checkNotifications, jsInvoker_, instance_);
    }
    jsi::Value request(jsi::Runtime &rt, jsi::String permission) override {
      static_assert(
          bridging::getParameterCount(&T::request) == 2,
          "Expected request(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::request, jsInvoker_, instance_, std::move(permission));
    }
    jsi::Value requestNotifications(jsi::Runtime &rt, jsi::Array options) override {
      static_assert(
          bridging::getParameterCount(&T::requestNotifications) == 2,
          "Expected requestNotifications(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestNotifications, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value checkMultiple(jsi::Runtime &rt, jsi::Array permissions) override {
      static_assert(
          bridging::getParameterCount(&T::checkMultiple) == 2,
          "Expected checkMultiple(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::checkMultiple, jsInvoker_, instance_, std::move(permissions));
    }
    jsi::Value requestMultiple(jsi::Runtime &rt, jsi::Array permissions) override {
      static_assert(
          bridging::getParameterCount(&T::requestMultiple) == 2,
          "Expected requestMultiple(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestMultiple, jsInvoker_, instance_, std::move(permissions));
    }
    jsi::Value shouldShowRequestRationale(jsi::Runtime &rt, jsi::String permission) override {
      static_assert(
          bridging::getParameterCount(&T::shouldShowRequestRationale) == 2,
          "Expected shouldShowRequestRationale(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::shouldShowRequestRationale, jsInvoker_, instance_, std::move(permission));
    }
    jsi::Value checkLocationAccuracy(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::checkLocationAccuracy) == 1,
          "Expected checkLocationAccuracy(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::checkLocationAccuracy, jsInvoker_, instance_);
    }
    jsi::Value openPhotoPicker(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::openPhotoPicker) == 1,
          "Expected openPhotoPicker(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openPhotoPicker, jsInvoker_, instance_);
    }
    jsi::Value requestLocationAccuracy(jsi::Runtime &rt, jsi::String purposeKey) override {
      static_assert(
          bridging::getParameterCount(&T::requestLocationAccuracy) == 2,
          "Expected requestLocationAccuracy(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestLocationAccuracy, jsInvoker_, instance_, std::move(purposeKey));
    }

  private:
    friend class NativeRNPermissionsCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
