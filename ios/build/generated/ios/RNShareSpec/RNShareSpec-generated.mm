/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNShareSpec.h"


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRNShareSpecJSI_open(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "open", @selector(open:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNShareSpecJSI_shareSingle(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "shareSingle", @selector(shareSingle:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNShareSpecJSI_isPackageInstalled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "isPackageInstalled", @selector(isPackageInstalled:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNShareSpecJSI_isBase64File(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "isBase64File", @selector(isBase64File:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNShareSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
    }

  NativeRNShareSpecJSI::NativeRNShareSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["open"] = MethodMetadata {1, __hostFunction_NativeRNShareSpecJSI_open};
        
        
        methodMap_["shareSingle"] = MethodMetadata {1, __hostFunction_NativeRNShareSpecJSI_shareSingle};
        
        
        methodMap_["isPackageInstalled"] = MethodMetadata {1, __hostFunction_NativeRNShareSpecJSI_isPackageInstalled};
        
        
        methodMap_["isBase64File"] = MethodMetadata {1, __hostFunction_NativeRNShareSpecJSI_isBase64File};
        
        
        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeRNShareSpecJSI_getConstants};
        
  }
} // namespace facebook::react
