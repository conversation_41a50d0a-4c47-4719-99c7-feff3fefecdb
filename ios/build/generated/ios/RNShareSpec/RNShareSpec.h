/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of RNShareSpec symbols
#ifndef RNShareSpec_H
#define RNShareSpec_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>

namespace JS {
  namespace NativeRNShare {
    struct Constants {

      struct Builder {
        struct Input {
          NSString *FACEBOOK;
          NSString *FACEBOOKSTORIES;
          NSString *TWITTER;
          NSString *GOOGLEPLUS;
          NSString *WHATSAPP;
          NSString *INSTAGRAM;
          NSString *INSTAGRAMSTORIES;
          NSString *TELEGRAM;
          NSString *EMAIL;
          NSString *MESSENGER;
          NSString *VIBER;
          NSString *PAGESMANAGER;
          NSString *WHATSAPPBUSINESS;
          NSString *PINTEREST;
          NSString *LINKEDIN;
          NSString *SNAPCHAT;
          NSString *SHARE_BACKGROUND_IMAGE;
          NSString *SHARE_BACKGROUND_VIDEO;
          NSString *SHARE_STICKER_IMAGE;
          NSString *SHARE_BACKGROUND_AND_STICKER_IMAGE;
          NSString *SMS;
          NSString *GENERIC;
          NSString *DISCORD;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeRNShareSpec <RCTBridgeModule, RCTTurboModule>

- (void)open:(NSDictionary *)options
     resolve:(RCTPromiseResolveBlock)resolve
      reject:(RCTPromiseRejectBlock)reject;
- (void)shareSingle:(NSDictionary *)options
            resolve:(RCTPromiseResolveBlock)resolve
             reject:(RCTPromiseRejectBlock)reject;
- (void)isPackageInstalled:(NSString *)packagename
                   resolve:(RCTPromiseResolveBlock)resolve
                    reject:(RCTPromiseRejectBlock)reject;
- (void)isBase64File:(NSString *)url
             resolve:(RCTPromiseResolveBlock)resolve
              reject:(RCTPromiseRejectBlock)reject;
- (facebook::react::ModuleConstants<JS::NativeRNShare::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeRNShare::Constants::Builder>)getConstants;

@end
namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeRNShare'
   */
  class JSI_EXPORT NativeRNShareSpecJSI : public ObjCTurboModule {
  public:
    NativeRNShareSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
inline JS::NativeRNShare::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto FACEBOOK = i.FACEBOOK;
  d[@"FACEBOOK"] = FACEBOOK;
  auto FACEBOOKSTORIES = i.FACEBOOKSTORIES;
  d[@"FACEBOOKSTORIES"] = FACEBOOKSTORIES;
  auto TWITTER = i.TWITTER;
  d[@"TWITTER"] = TWITTER;
  auto GOOGLEPLUS = i.GOOGLEPLUS;
  d[@"GOOGLEPLUS"] = GOOGLEPLUS;
  auto WHATSAPP = i.WHATSAPP;
  d[@"WHATSAPP"] = WHATSAPP;
  auto INSTAGRAM = i.INSTAGRAM;
  d[@"INSTAGRAM"] = INSTAGRAM;
  auto INSTAGRAMSTORIES = i.INSTAGRAMSTORIES;
  d[@"INSTAGRAMSTORIES"] = INSTAGRAMSTORIES;
  auto TELEGRAM = i.TELEGRAM;
  d[@"TELEGRAM"] = TELEGRAM;
  auto EMAIL = i.EMAIL;
  d[@"EMAIL"] = EMAIL;
  auto MESSENGER = i.MESSENGER;
  d[@"MESSENGER"] = MESSENGER;
  auto VIBER = i.VIBER;
  d[@"VIBER"] = VIBER;
  auto PAGESMANAGER = i.PAGESMANAGER;
  d[@"PAGESMANAGER"] = PAGESMANAGER;
  auto WHATSAPPBUSINESS = i.WHATSAPPBUSINESS;
  d[@"WHATSAPPBUSINESS"] = WHATSAPPBUSINESS;
  auto PINTEREST = i.PINTEREST;
  d[@"PINTEREST"] = PINTEREST;
  auto LINKEDIN = i.LINKEDIN;
  d[@"LINKEDIN"] = LINKEDIN;
  auto SNAPCHAT = i.SNAPCHAT;
  d[@"SNAPCHAT"] = SNAPCHAT;
  auto SHARE_BACKGROUND_IMAGE = i.SHARE_BACKGROUND_IMAGE;
  d[@"SHARE_BACKGROUND_IMAGE"] = SHARE_BACKGROUND_IMAGE;
  auto SHARE_BACKGROUND_VIDEO = i.SHARE_BACKGROUND_VIDEO;
  d[@"SHARE_BACKGROUND_VIDEO"] = SHARE_BACKGROUND_VIDEO;
  auto SHARE_STICKER_IMAGE = i.SHARE_STICKER_IMAGE;
  d[@"SHARE_STICKER_IMAGE"] = SHARE_STICKER_IMAGE;
  auto SHARE_BACKGROUND_AND_STICKER_IMAGE = i.SHARE_BACKGROUND_AND_STICKER_IMAGE;
  d[@"SHARE_BACKGROUND_AND_STICKER_IMAGE"] = SHARE_BACKGROUND_AND_STICKER_IMAGE;
  auto SMS = i.SMS;
  d[@"SMS"] = SMS;
  auto GENERIC = i.GENERIC;
  d[@"GENERIC"] = GENERIC;
  auto DISCORD = i.DISCORD;
  d[@"DISCORD"] = DISCORD;
  return d;
}) {}
inline JS::NativeRNShare::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
#endif // RNShareSpec_H
