/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNShareSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeRNShareCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNShareCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNShareCxxSpecJSI_open(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNShareCxxSpecJSI *>(&turboModule)->open(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeRNShareCxxSpecJSI_shareSingle(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNShareCxxSpecJSI *>(&turboModule)->shareSingle(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeRNShareCxxSpecJSI_isPackageInstalled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNShareCxxSpecJSI *>(&turboModule)->isPackageInstalled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeRNShareCxxSpecJSI_isBase64File(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNShareCxxSpecJSI *>(&turboModule)->isBase64File(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}

NativeRNShareCxxSpecJSI::NativeRNShareCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNShare", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeRNShareCxxSpecJSI_getConstants};
  methodMap_["open"] = MethodMetadata {1, __hostFunction_NativeRNShareCxxSpecJSI_open};
  methodMap_["shareSingle"] = MethodMetadata {1, __hostFunction_NativeRNShareCxxSpecJSI_shareSingle};
  methodMap_["isPackageInstalled"] = MethodMetadata {1, __hostFunction_NativeRNShareCxxSpecJSI_isPackageInstalled};
  methodMap_["isBase64File"] = MethodMetadata {1, __hostFunction_NativeRNShareCxxSpecJSI_isBase64File};
}


} // namespace facebook::react
