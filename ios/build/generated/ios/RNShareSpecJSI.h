/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeRNShareCxxSpecJSI : public TurboModule {
protected:
  NativeRNShareCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value open(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual jsi::Value shareSingle(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual jsi::Value isPackageInstalled(jsi::Runtime &rt, jsi::String packagename) = 0;
  virtual jsi::Value isBase64File(jsi::Runtime &rt, jsi::String url) = 0;

};

template <typename T>
class JSI_EXPORT NativeRNShareCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNShare";

protected:
  NativeRNShareCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRNShareCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRNShareCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRNShareCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value open(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::open) == 2,
          "Expected open(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::open, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value shareSingle(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::shareSingle) == 2,
          "Expected shareSingle(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::shareSingle, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value isPackageInstalled(jsi::Runtime &rt, jsi::String packagename) override {
      static_assert(
          bridging::getParameterCount(&T::isPackageInstalled) == 2,
          "Expected isPackageInstalled(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::isPackageInstalled, jsInvoker_, instance_, std::move(packagename));
    }
    jsi::Value isBase64File(jsi::Runtime &rt, jsi::String url) override {
      static_assert(
          bridging::getParameterCount(&T::isBase64File) == 2,
          "Expected isBase64File(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::isBase64File, jsInvoker_, instance_, std::move(url));
    }

  private:
    friend class NativeRNShareCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
