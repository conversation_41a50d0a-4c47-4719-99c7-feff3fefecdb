/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "ReactNativeBlobUtilSpec.h"


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_fetchBlobForm(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "fetchBlobForm", @selector(fetchBlobForm:taskId:method:url:headers:form:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_fetchBlob(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "fetchBlob", @selector(fetchBlob:taskId:method:url:headers:body:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_createFile(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "createFile", @selector(createFile:data:encoding:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_createFileASCII(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "createFileASCII", @selector(createFileASCII:data:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_pathForAppGroup(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "pathForAppGroup", @selector(pathForAppGroup:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_syncPathAppGroup(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, StringKind, "syncPathAppGroup", @selector(syncPathAppGroup:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_exists(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "exists", @selector(exists:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_writeFile(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "writeFile", @selector(writeFile:encoding:data:transformFile:append:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_writeFileArray(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "writeFileArray", @selector(writeFileArray:data:append:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_writeStream(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "writeStream", @selector(writeStream:withEncoding:appendData:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_writeArrayChunk(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "writeArrayChunk", @selector(writeArrayChunk:withArray:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_writeChunk(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "writeChunk", @selector(writeChunk:withData:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_closeStream(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "closeStream", @selector(closeStream:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_unlink(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "unlink", @selector(unlink:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_removeSession(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeSession", @selector(removeSession:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_ls(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "ls", @selector(ls:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_stat(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stat", @selector(stat:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_lstat(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "lstat", @selector(lstat:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_cp(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cp", @selector(cp:dest:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_mv(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "mv", @selector(mv:dest:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_mkdir(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "mkdir", @selector(mkdir:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_readFile(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "readFile", @selector(readFile:encoding:transformFile:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_hash(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "hash", @selector(hash:algorithm:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_readStream(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "readStream", @selector(readStream:encoding:bufferSize:tick:streamId:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_getEnvironmentDirs(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getEnvironmentDirs", @selector(getEnvironmentDirs:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_cancelRequest(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cancelRequest", @selector(cancelRequest:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_enableProgressReport(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "enableProgressReport", @selector(enableProgressReport:interval:count:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_enableUploadProgressReport(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "enableUploadProgressReport", @selector(enableUploadProgressReport:interval:count:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_slice(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "slice", @selector(slice:dest:start:end:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_presentOptionsMenu(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "presentOptionsMenu", @selector(presentOptionsMenu:scheme:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_presentOpenInMenu(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "presentOpenInMenu", @selector(presentOpenInMenu:scheme:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_presentPreview(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "presentPreview", @selector(presentPreview:scheme:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_excludeFromBackupKey(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "excludeFromBackupKey", @selector(excludeFromBackupKey:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_df(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "df", @selector(df:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_emitExpiredEvent(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "emitExpiredEvent", @selector(emitExpiredEvent:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_actionViewIntent(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "actionViewIntent", @selector(actionViewIntent:mime:chooserTitle:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_addCompleteDownload(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "addCompleteDownload", @selector(addCompleteDownload:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_copyToInternal(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "copyToInternal", @selector(copyToInternal:destpath:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_copyToMediaStore(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "copyToMediaStore", @selector(copyToMediaStore:mt:path:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_createMediaFile(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "createMediaFile", @selector(createMediaFile:mt:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_getBlob(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getBlob", @selector(getBlob:encoding:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_getContentIntent(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getContentIntent", @selector(getContentIntent:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_getSDCardDir(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getSDCardDir", @selector(getSDCardDir:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_getSDCardApplicationDir(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getSDCardApplicationDir", @selector(getSDCardApplicationDir:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_scanFile(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "scanFile", @selector(scanFile:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_writeToMediaFile(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "writeToMediaFile", @selector(writeToMediaFile:path:transformFile:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeBlobUtilsSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
    }

  NativeBlobUtilsSpecJSI::NativeBlobUtilsSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["fetchBlobForm"] = MethodMetadata {7, __hostFunction_NativeBlobUtilsSpecJSI_fetchBlobForm};
        
        
        methodMap_["fetchBlob"] = MethodMetadata {7, __hostFunction_NativeBlobUtilsSpecJSI_fetchBlob};
        
        
        methodMap_["createFile"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_createFile};
        
        
        methodMap_["createFileASCII"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_createFileASCII};
        
        
        methodMap_["pathForAppGroup"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_pathForAppGroup};
        
        
        methodMap_["syncPathAppGroup"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_syncPathAppGroup};
        
        
        methodMap_["exists"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_exists};
        
        
        methodMap_["writeFile"] = MethodMetadata {5, __hostFunction_NativeBlobUtilsSpecJSI_writeFile};
        
        
        methodMap_["writeFileArray"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_writeFileArray};
        
        
        methodMap_["writeStream"] = MethodMetadata {4, __hostFunction_NativeBlobUtilsSpecJSI_writeStream};
        
        
        methodMap_["writeArrayChunk"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_writeArrayChunk};
        
        
        methodMap_["writeChunk"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_writeChunk};
        
        
        methodMap_["closeStream"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_closeStream};
        
        
        methodMap_["unlink"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_unlink};
        
        
        methodMap_["removeSession"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_removeSession};
        
        
        methodMap_["ls"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_ls};
        
        
        methodMap_["stat"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_stat};
        
        
        methodMap_["lstat"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_lstat};
        
        
        methodMap_["cp"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_cp};
        
        
        methodMap_["mv"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_mv};
        
        
        methodMap_["mkdir"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_mkdir};
        
        
        methodMap_["readFile"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_readFile};
        
        
        methodMap_["hash"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_hash};
        
        
        methodMap_["readStream"] = MethodMetadata {5, __hostFunction_NativeBlobUtilsSpecJSI_readStream};
        
        
        methodMap_["getEnvironmentDirs"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_getEnvironmentDirs};
        
        
        methodMap_["cancelRequest"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_cancelRequest};
        
        
        methodMap_["enableProgressReport"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_enableProgressReport};
        
        
        methodMap_["enableUploadProgressReport"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_enableUploadProgressReport};
        
        
        methodMap_["slice"] = MethodMetadata {4, __hostFunction_NativeBlobUtilsSpecJSI_slice};
        
        
        methodMap_["presentOptionsMenu"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_presentOptionsMenu};
        
        
        methodMap_["presentOpenInMenu"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_presentOpenInMenu};
        
        
        methodMap_["presentPreview"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_presentPreview};
        
        
        methodMap_["excludeFromBackupKey"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_excludeFromBackupKey};
        
        
        methodMap_["df"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_df};
        
        
        methodMap_["emitExpiredEvent"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_emitExpiredEvent};
        
        
        methodMap_["actionViewIntent"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_actionViewIntent};
        
        
        methodMap_["addCompleteDownload"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_addCompleteDownload};
        
        
        methodMap_["copyToInternal"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_copyToInternal};
        
        
        methodMap_["copyToMediaStore"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_copyToMediaStore};
        
        
        methodMap_["createMediaFile"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_createMediaFile};
        
        
        methodMap_["getBlob"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_getBlob};
        
        
        methodMap_["getContentIntent"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_getContentIntent};
        
        
        methodMap_["getSDCardDir"] = MethodMetadata {0, __hostFunction_NativeBlobUtilsSpecJSI_getSDCardDir};
        
        
        methodMap_["getSDCardApplicationDir"] = MethodMetadata {0, __hostFunction_NativeBlobUtilsSpecJSI_getSDCardApplicationDir};
        
        
        methodMap_["scanFile"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsSpecJSI_scanFile};
        
        
        methodMap_["writeToMediaFile"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsSpecJSI_writeToMediaFile};
        
        
        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_addListener};
        
        
        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsSpecJSI_removeListeners};
        
        
        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeBlobUtilsSpecJSI_getConstants};
        
  }
} // namespace facebook::react
