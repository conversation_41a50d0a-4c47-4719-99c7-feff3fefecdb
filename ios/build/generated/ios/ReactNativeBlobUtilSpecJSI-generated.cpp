/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "ReactNativeBlobUtilSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_fetchBlobForm(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->fetchBlobForm(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asString(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asString(rt),
    count <= 4 ? throw jsi::JSError(rt, "Expected argument in position 4 to be passed") : args[4].asObject(rt),
    count <= 5 ? throw jsi::JSError(rt, "Expected argument in position 5 to be passed") : args[5].asObject(rt).asArray(rt),
    count <= 6 ? throw jsi::JSError(rt, "Expected argument in position 6 to be passed") : args[6].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_fetchBlob(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->fetchBlob(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asString(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asString(rt),
    count <= 4 ? throw jsi::JSError(rt, "Expected argument in position 4 to be passed") : args[4].asObject(rt),
    count <= 5 ? throw jsi::JSError(rt, "Expected argument in position 5 to be passed") : args[5].asString(rt),
    count <= 6 ? throw jsi::JSError(rt, "Expected argument in position 6 to be passed") : args[6].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_createFile(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->createFile(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_createFileASCII(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->createFileASCII(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asArray(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_pathForAppGroup(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->pathForAppGroup(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_syncPathAppGroup(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->syncPathAppGroup(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_exists(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->exists(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_writeFile(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->writeFile(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asString(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asBool(),
    count <= 4 ? throw jsi::JSError(rt, "Expected argument in position 4 to be passed") : args[4].asBool()
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_writeFileArray(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->writeFileArray(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asArray(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asBool()
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_writeStream(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->writeStream(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asBool(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_writeArrayChunk(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->writeArrayChunk(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asArray(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_writeChunk(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->writeChunk(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_closeStream(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->closeStream(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_unlink(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->unlink(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_removeSession(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->removeSession(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_ls(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->ls(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_stat(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->stat(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_lstat(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->lstat(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_cp(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->cp(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_mv(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->mv(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_mkdir(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->mkdir(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_readFile(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->readFile(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asBool()
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_hash(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->hash(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_readStream(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->readStream(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber(),
    count <= 4 ? throw jsi::JSError(rt, "Expected argument in position 4 to be passed") : args[4].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_getEnvironmentDirs(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->getEnvironmentDirs(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_cancelRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->cancelRequest(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_enableProgressReport(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->enableProgressReport(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_enableUploadProgressReport(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->enableUploadProgressReport(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_slice(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->slice(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber()
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_presentOptionsMenu(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->presentOptionsMenu(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_presentOpenInMenu(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->presentOpenInMenu(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_presentPreview(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->presentPreview(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_excludeFromBackupKey(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->excludeFromBackupKey(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_df(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->df(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_emitExpiredEvent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->emitExpiredEvent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_actionViewIntent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->actionViewIntent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_addCompleteDownload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->addCompleteDownload(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_copyToInternal(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->copyToInternal(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_copyToMediaStore(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->copyToMediaStore(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_createMediaFile(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->createMediaFile(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_getBlob(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->getBlob(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_getContentIntent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->getContentIntent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_getSDCardDir(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->getSDCardDir(
    rt
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_getSDCardApplicationDir(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->getSDCardApplicationDir(
    rt
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_scanFile(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->scanFile(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_writeToMediaFile(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->writeToMediaFile(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asBool()
  );
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobUtilsCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobUtilsCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeBlobUtilsCxxSpecJSI::NativeBlobUtilsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ReactNativeBlobUtil", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeBlobUtilsCxxSpecJSI_getConstants};
  methodMap_["fetchBlobForm"] = MethodMetadata {7, __hostFunction_NativeBlobUtilsCxxSpecJSI_fetchBlobForm};
  methodMap_["fetchBlob"] = MethodMetadata {7, __hostFunction_NativeBlobUtilsCxxSpecJSI_fetchBlob};
  methodMap_["createFile"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_createFile};
  methodMap_["createFileASCII"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_createFileASCII};
  methodMap_["pathForAppGroup"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_pathForAppGroup};
  methodMap_["syncPathAppGroup"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_syncPathAppGroup};
  methodMap_["exists"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_exists};
  methodMap_["writeFile"] = MethodMetadata {5, __hostFunction_NativeBlobUtilsCxxSpecJSI_writeFile};
  methodMap_["writeFileArray"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_writeFileArray};
  methodMap_["writeStream"] = MethodMetadata {4, __hostFunction_NativeBlobUtilsCxxSpecJSI_writeStream};
  methodMap_["writeArrayChunk"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_writeArrayChunk};
  methodMap_["writeChunk"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_writeChunk};
  methodMap_["closeStream"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_closeStream};
  methodMap_["unlink"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_unlink};
  methodMap_["removeSession"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_removeSession};
  methodMap_["ls"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_ls};
  methodMap_["stat"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_stat};
  methodMap_["lstat"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_lstat};
  methodMap_["cp"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_cp};
  methodMap_["mv"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_mv};
  methodMap_["mkdir"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_mkdir};
  methodMap_["readFile"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_readFile};
  methodMap_["hash"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_hash};
  methodMap_["readStream"] = MethodMetadata {5, __hostFunction_NativeBlobUtilsCxxSpecJSI_readStream};
  methodMap_["getEnvironmentDirs"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_getEnvironmentDirs};
  methodMap_["cancelRequest"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_cancelRequest};
  methodMap_["enableProgressReport"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_enableProgressReport};
  methodMap_["enableUploadProgressReport"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_enableUploadProgressReport};
  methodMap_["slice"] = MethodMetadata {4, __hostFunction_NativeBlobUtilsCxxSpecJSI_slice};
  methodMap_["presentOptionsMenu"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_presentOptionsMenu};
  methodMap_["presentOpenInMenu"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_presentOpenInMenu};
  methodMap_["presentPreview"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_presentPreview};
  methodMap_["excludeFromBackupKey"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_excludeFromBackupKey};
  methodMap_["df"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_df};
  methodMap_["emitExpiredEvent"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_emitExpiredEvent};
  methodMap_["actionViewIntent"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_actionViewIntent};
  methodMap_["addCompleteDownload"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_addCompleteDownload};
  methodMap_["copyToInternal"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_copyToInternal};
  methodMap_["copyToMediaStore"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_copyToMediaStore};
  methodMap_["createMediaFile"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_createMediaFile};
  methodMap_["getBlob"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_getBlob};
  methodMap_["getContentIntent"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_getContentIntent};
  methodMap_["getSDCardDir"] = MethodMetadata {0, __hostFunction_NativeBlobUtilsCxxSpecJSI_getSDCardDir};
  methodMap_["getSDCardApplicationDir"] = MethodMetadata {0, __hostFunction_NativeBlobUtilsCxxSpecJSI_getSDCardApplicationDir};
  methodMap_["scanFile"] = MethodMetadata {2, __hostFunction_NativeBlobUtilsCxxSpecJSI_scanFile};
  methodMap_["writeToMediaFile"] = MethodMetadata {3, __hostFunction_NativeBlobUtilsCxxSpecJSI_writeToMediaFile};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeBlobUtilsCxxSpecJSI_removeListeners};
}


} // namespace facebook::react
