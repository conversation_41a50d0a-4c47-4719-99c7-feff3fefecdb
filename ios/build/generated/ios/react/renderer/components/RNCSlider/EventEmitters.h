
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterH.js
 */
#pragma once

#include <react/renderer/components/view/ViewEventEmitter.h>


namespace facebook::react {
class RNCSliderEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnChange {
      Float value;
    bool fromUser;
    };

  struct OnRNCSliderSlidingStart {
      Float value;
    bool fromUser;
    };

  struct OnRNCSliderSlidingComplete {
      Float value;
    bool fromUser;
    };

  struct OnRNCSliderValueChange {
      Float value;
    bool fromUser;
    };
  void onChange(OnChange value) const;

  void onRNCSliderSlidingStart(OnRNCSliderSlidingStart value) const;

  void onRNCSliderSlidingComplete(OnRNCSliderSlidingComplete value) const;

  void onRNCSliderValueChange(OnRNCSliderValueChange value) const;
};
} // namespace facebook::react
