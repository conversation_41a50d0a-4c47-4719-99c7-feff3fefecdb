
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include <react/renderer/components/RNDateTimePickerCGen/Props.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

RNDateTimePickerProps::RNDateTimePickerProps(
    const PropsParserContext &context,
    const RNDateTimePickerProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    accentColor(convertRawProp(context, rawProps, "accentColor", sourceProps.accentColor, {})),
    date(convertRawProp(context, rawProps, "date", sourceProps.date, {0.0})),
    displayIOS(convertRawProp(context, rawProps, "displayIOS", sourceProps.displayIOS, {RNDateTimePickerDisplayIOS::Default})),
    locale(convertRawProp(context, rawProps, "locale", sourceProps.locale, {})),
    maximumDate(convertRawProp(context, rawProps, "maximumDate", sourceProps.maximumDate, {0.0})),
    minimumDate(convertRawProp(context, rawProps, "minimumDate", sourceProps.minimumDate, {0.0})),
    minuteInterval(convertRawProp(context, rawProps, "minuteInterval", sourceProps.minuteInterval, {0})),
    mode(convertRawProp(context, rawProps, "mode", sourceProps.mode, {RNDateTimePickerMode::Date})),
    textColor(convertRawProp(context, rawProps, "textColor", sourceProps.textColor, {})),
    themeVariant(convertRawProp(context, rawProps, "themeVariant", sourceProps.themeVariant, {RNDateTimePickerThemeVariant::Unspecified})),
    timeZoneName(convertRawProp(context, rawProps, "timeZoneName", sourceProps.timeZoneName, {})),
    timeZoneOffsetInMinutes(convertRawProp(context, rawProps, "timeZoneOffsetInMinutes", sourceProps.timeZoneOffsetInMinutes, {0.0})),
    enabled(convertRawProp(context, rawProps, "enabled", sourceProps.enabled, {true}))
      {}

} // namespace facebook::react
