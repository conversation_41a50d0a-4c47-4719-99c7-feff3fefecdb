
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/graphics/Color.h>

namespace facebook::react {

enum class RNDateTimePickerDisplayIOS { Default, Spinner, Compact, Inline };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNDateTimePickerDisplayIOS &result) {
  auto string = (std::string)value;
  if (string == "default") { result = RNDateTimePickerDisplayIOS::Default; return; }
  if (string == "spinner") { result = RNDateTimePickerDisplayIOS::Spinner; return; }
  if (string == "compact") { result = RNDateTimePickerDisplayIOS::Compact; return; }
  if (string == "inline") { result = RNDateTimePickerDisplayIOS::Inline; return; }
  abort();
}

static inline std::string toString(const RNDateTimePickerDisplayIOS &value) {
  switch (value) {
    case RNDateTimePickerDisplayIOS::Default: return "default";
    case RNDateTimePickerDisplayIOS::Spinner: return "spinner";
    case RNDateTimePickerDisplayIOS::Compact: return "compact";
    case RNDateTimePickerDisplayIOS::Inline: return "inline";
  }
}
enum class RNDateTimePickerMode { Date, Time, Datetime, Countdown };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNDateTimePickerMode &result) {
  auto string = (std::string)value;
  if (string == "date") { result = RNDateTimePickerMode::Date; return; }
  if (string == "time") { result = RNDateTimePickerMode::Time; return; }
  if (string == "datetime") { result = RNDateTimePickerMode::Datetime; return; }
  if (string == "countdown") { result = RNDateTimePickerMode::Countdown; return; }
  abort();
}

static inline std::string toString(const RNDateTimePickerMode &value) {
  switch (value) {
    case RNDateTimePickerMode::Date: return "date";
    case RNDateTimePickerMode::Time: return "time";
    case RNDateTimePickerMode::Datetime: return "datetime";
    case RNDateTimePickerMode::Countdown: return "countdown";
  }
}
enum class RNDateTimePickerThemeVariant { Dark, Light, Unspecified };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNDateTimePickerThemeVariant &result) {
  auto string = (std::string)value;
  if (string == "dark") { result = RNDateTimePickerThemeVariant::Dark; return; }
  if (string == "light") { result = RNDateTimePickerThemeVariant::Light; return; }
  if (string == "unspecified") { result = RNDateTimePickerThemeVariant::Unspecified; return; }
  abort();
}

static inline std::string toString(const RNDateTimePickerThemeVariant &value) {
  switch (value) {
    case RNDateTimePickerThemeVariant::Dark: return "dark";
    case RNDateTimePickerThemeVariant::Light: return "light";
    case RNDateTimePickerThemeVariant::Unspecified: return "unspecified";
  }
}

class RNDateTimePickerProps final : public ViewProps {
 public:
  RNDateTimePickerProps() = default;
  RNDateTimePickerProps(const PropsParserContext& context, const RNDateTimePickerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  SharedColor accentColor{};
  double date{0.0};
  RNDateTimePickerDisplayIOS displayIOS{RNDateTimePickerDisplayIOS::Default};
  std::string locale{};
  double maximumDate{0.0};
  double minimumDate{0.0};
  int minuteInterval{0};
  RNDateTimePickerMode mode{RNDateTimePickerMode::Date};
  SharedColor textColor{};
  RNDateTimePickerThemeVariant themeVariant{RNDateTimePickerThemeVariant::Unspecified};
  std::string timeZoneName{};
  double timeZoneOffsetInMinutes{0.0};
  bool enabled{true};
};

} // namespace facebook::react
