
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterCpp.js
 */

#include <react/renderer/components/lottiereactnative/EventEmitters.h>


namespace facebook::react {

void LottieAnimationViewEventEmitter::onAnimationFinish(OnAnimationFinish $event) const {
  dispatchEvent("onAnimationFinish", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "isCancelled", $event.isCancelled);
    return $payload;
  });
}


void LottieAnimationViewEventEmitter::onAnimationFailure(OnAnimationFailure $event) const {
  dispatchEvent("onAnimationFailure", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "error", $event.error);
    return $payload;
  });
}


void LottieAnimationViewEventEmitter::onAnimationLoaded(OnAnimationLoaded $event) const {
  dispatchEvent("onAnimationLoaded", [](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    
    return $payload;
  });
}

} // namespace facebook::react
