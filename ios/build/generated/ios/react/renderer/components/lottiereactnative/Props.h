
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <react/renderer/graphics/Color.h>
#include <vector>

namespace facebook::react {

struct LottieAnimationViewColorFiltersStruct {
  std::string keypath{};
  SharedColor color{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, LottieAnimationViewColorFiltersStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_keypath = map.find("keypath");
  if (tmp_keypath != map.end()) {
    fromRawValue(context, tmp_keypath->second, result.keypath);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
}

static inline std::string toString(const LottieAnimationViewColorFiltersStruct &value) {
  return "[Object LottieAnimationViewColorFiltersStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<LottieAnimationViewColorFiltersStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    LottieAnimationViewColorFiltersStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}


struct LottieAnimationViewDummyStruct {
  bool dummy{false};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, LottieAnimationViewDummyStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_dummy = map.find("dummy");
  if (tmp_dummy != map.end()) {
    fromRawValue(context, tmp_dummy->second, result.dummy);
  }
}

static inline std::string toString(const LottieAnimationViewDummyStruct &value) {
  return "[Object LottieAnimationViewDummyStruct]";
}

struct LottieAnimationViewTextFiltersAndroidStruct {
  std::string find{};
  std::string replace{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, LottieAnimationViewTextFiltersAndroidStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_find = map.find("find");
  if (tmp_find != map.end()) {
    fromRawValue(context, tmp_find->second, result.find);
  }
  auto tmp_replace = map.find("replace");
  if (tmp_replace != map.end()) {
    fromRawValue(context, tmp_replace->second, result.replace);
  }
}

static inline std::string toString(const LottieAnimationViewTextFiltersAndroidStruct &value) {
  return "[Object LottieAnimationViewTextFiltersAndroidStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<LottieAnimationViewTextFiltersAndroidStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    LottieAnimationViewTextFiltersAndroidStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}


struct LottieAnimationViewTextFiltersIOSStruct {
  std::string keypath{};
  std::string text{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, LottieAnimationViewTextFiltersIOSStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_keypath = map.find("keypath");
  if (tmp_keypath != map.end()) {
    fromRawValue(context, tmp_keypath->second, result.keypath);
  }
  auto tmp_text = map.find("text");
  if (tmp_text != map.end()) {
    fromRawValue(context, tmp_text->second, result.text);
  }
}

static inline std::string toString(const LottieAnimationViewTextFiltersIOSStruct &value) {
  return "[Object LottieAnimationViewTextFiltersIOSStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<LottieAnimationViewTextFiltersIOSStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    LottieAnimationViewTextFiltersIOSStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}

class LottieAnimationViewProps final : public ViewProps {
 public:
  LottieAnimationViewProps() = default;
  LottieAnimationViewProps(const PropsParserContext& context, const LottieAnimationViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string resizeMode{};
  std::string renderMode{};
  std::string sourceName{};
  std::string sourceJson{};
  std::string sourceURL{};
  std::string sourceDotLottieURI{};
  std::string imageAssetsFolder{};
  Float progress{0.0};
  double speed{0.0};
  bool loop{false};
  bool autoPlay{false};
  bool enableMergePathsAndroidForKitKatAndAbove{false};
  bool enableSafeModeAndroid{false};
  bool hardwareAccelerationAndroid{false};
  bool cacheComposition{false};
  std::vector<LottieAnimationViewColorFiltersStruct> colorFilters{};
  LottieAnimationViewDummyStruct dummy{};
  std::vector<LottieAnimationViewTextFiltersAndroidStruct> textFiltersAndroid{};
  std::vector<LottieAnimationViewTextFiltersIOSStruct> textFiltersIOS{};
};

} // namespace facebook::react
