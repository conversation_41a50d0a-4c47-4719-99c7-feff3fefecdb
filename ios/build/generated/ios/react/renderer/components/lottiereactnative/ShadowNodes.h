
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/lottiereactnative/EventEmitters.h>
#include <react/renderer/components/lottiereactnative/Props.h>
#include <react/renderer/components/lottiereactnative/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char LottieAnimationViewComponentName[];

/*
 * `ShadowNode` for <LottieAnimationView> component.
 */
using LottieAnimationViewShadowNode = ConcreteViewShadowNode<
    LottieAnimationViewComponentName,
    LottieAnimationViewProps,
    LottieAnimationViewEventEmitter,
    LottieAnimationViewState>;

} // namespace facebook::react
