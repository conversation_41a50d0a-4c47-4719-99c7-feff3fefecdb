
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterH.js
 */
#pragma once

#include <react/renderer/components/view/ViewEventEmitter.h>


namespace facebook::react {
class RNCViewPagerEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnPageScroll {
      double position;
    double offset;
    };

  struct OnPageSelected {
      double position;
    };

  enum class OnPageScrollStateChangedPageScrollState {
    Idle,
    Dragging,
    Settling
  };

  static char const *toString(const OnPageScrollStateChangedPageScrollState value) {
    switch (value) {
      case OnPageScrollStateChangedPageScrollState::Idle: return "idle";
      case OnPageScrollStateChangedPageScrollState::Dragging: return "dragging";
      case OnPageScrollStateChangedPageScrollState::Settling: return "settling";
    }
  }

  struct OnPageScrollStateChanged {
      OnPageScrollStateChangedPageScrollState pageScrollState;
    };
  void onPageScroll(OnPageScroll value) const;

  void onPageSelected(OnPageSelected value) const;

  void onPageScrollStateChanged(OnPageScrollStateChanged value) const;
};
} // namespace facebook::react
