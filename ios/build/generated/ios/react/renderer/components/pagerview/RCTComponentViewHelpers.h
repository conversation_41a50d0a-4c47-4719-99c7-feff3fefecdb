/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTRNCViewPagerViewProtocol <NSObject>
- (void)setPage:(NSInteger)selectedPage;
- (void)setPageWithoutAnimation:(NSInteger)selectedPage;
- (void)setScrollEnabledImperatively:(BOOL)scrollEnabled;
@end

RCT_EXTERN inline void RCTRNCViewPagerHandleCommand(
  id<RCTRNCViewPagerViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"setPage"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCViewPager", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"number", @"RNCViewPager", commandName, @"1st")) {
    return;
  }
#endif
  NSInteger selectedPage = [(NSNumber *)arg0 intValue];

  [componentView setPage:selectedPage];
  return;
}

if ([commandName isEqualToString:@"setPageWithoutAnimation"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCViewPager", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"number", @"RNCViewPager", commandName, @"1st")) {
    return;
  }
#endif
  NSInteger selectedPage = [(NSNumber *)arg0 intValue];

  [componentView setPageWithoutAnimation:selectedPage];
  return;
}

if ([commandName isEqualToString:@"setScrollEnabledImperatively"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCViewPager", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"RNCViewPager", commandName, @"1st")) {
    return;
  }
#endif
  BOOL scrollEnabled = [(NSNumber *)arg0 boolValue];

  [componentView setScrollEnabledImperatively:scrollEnabled];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNCViewPager", commandName);
#endif
}

NS_ASSUME_NONNULL_END