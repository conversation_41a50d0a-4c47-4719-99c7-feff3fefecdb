
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/rnscreens/EventEmitters.h>
#include <react/renderer/components/rnscreens/Props.h>
#include <react/renderer/components/rnscreens/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNSFullWindowOverlayComponentName[];

/*
 * `ShadowNode` for <RNSFullWindowOverlay> component.
 */
using RNSFullWindowOverlayShadowNode = ConcreteViewShadowNode<
    RNSFullWindowOverlayComponentName,
    RNSFullWindowOverlayProps,
    RNSFullWindowOverlayEventEmitter,
    RNSFullWindowOverlayState>;

JSI_EXPORT extern const char RNSScreenContainerComponentName[];

/*
 * `ShadowNode` for <RNSScreenContainer> component.
 */
using RNSScreenContainerShadowNode = ConcreteViewShadowNode<
    RNSScreenContainerComponentName,
    RNSScreenContainerProps,
    RNSScreenContainerEventEmitter,
    RNSScreenContainerState>;

JSI_EXPORT extern const char RNSScreenNavigationContainerComponentName[];

/*
 * `ShadowNode` for <RNSScreenNavigationContainer> component.
 */
using RNSScreenNavigationContainerShadowNode = ConcreteViewShadowNode<
    RNSScreenNavigationContainerComponentName,
    RNSScreenNavigationContainerProps,
    RNSScreenNavigationContainerEventEmitter,
    RNSScreenNavigationContainerState>;

JSI_EXPORT extern const char RNSScreenStackHeaderConfigComponentName[];

/*
 * `ShadowNode` for <RNSScreenStackHeaderConfig> component.
 */
using RNSScreenStackHeaderConfigShadowNode = ConcreteViewShadowNode<
    RNSScreenStackHeaderConfigComponentName,
    RNSScreenStackHeaderConfigProps,
    RNSScreenStackHeaderConfigEventEmitter,
    RNSScreenStackHeaderConfigState>;

JSI_EXPORT extern const char RNSScreenStackHeaderSubviewComponentName[];

/*
 * `ShadowNode` for <RNSScreenStackHeaderSubview> component.
 */
using RNSScreenStackHeaderSubviewShadowNode = ConcreteViewShadowNode<
    RNSScreenStackHeaderSubviewComponentName,
    RNSScreenStackHeaderSubviewProps,
    RNSScreenStackHeaderSubviewEventEmitter,
    RNSScreenStackHeaderSubviewState>;

JSI_EXPORT extern const char RNSScreenStackComponentName[];

/*
 * `ShadowNode` for <RNSScreenStack> component.
 */
using RNSScreenStackShadowNode = ConcreteViewShadowNode<
    RNSScreenStackComponentName,
    RNSScreenStackProps,
    RNSScreenStackEventEmitter,
    RNSScreenStackState>;

JSI_EXPORT extern const char RNSSearchBarComponentName[];

/*
 * `ShadowNode` for <RNSSearchBar> component.
 */
using RNSSearchBarShadowNode = ConcreteViewShadowNode<
    RNSSearchBarComponentName,
    RNSSearchBarProps,
    RNSSearchBarEventEmitter,
    RNSSearchBarState>;

} // namespace facebook::react
