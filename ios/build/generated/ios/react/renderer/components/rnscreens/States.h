/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateStateH.js
 */
#pragma once

#ifdef ANDROID
#include <folly/dynamic.h>
#endif

namespace facebook::react {

class RNSFullWindowOverlayState {
public:
  RNSFullWindowOverlayState() = default;

#ifdef ANDROID
  RNSFullWindowOverlayState(RNSFullWindowOverlayState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenContainerState {
public:
  RNSScreenContainerState() = default;

#ifdef ANDROID
  RNSScreenContainerState(RNSScreenContainerState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenNavigationContainerState {
public:
  RNSScreenNavigationContainerState() = default;

#ifdef ANDROID
  RNSScreenNavigationContainerState(RNSScreenNavigationContainerState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenStackHeaderConfigState {
public:
  RNSScreenStackHeaderConfigState() = default;

#ifdef ANDROID
  RNSScreenStackHeaderConfigState(RNSScreenStackHeaderConfigState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenStackHeaderSubviewState {
public:
  RNSScreenStackHeaderSubviewState() = default;

#ifdef ANDROID
  RNSScreenStackHeaderSubviewState(RNSScreenStackHeaderSubviewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenStackState {
public:
  RNSScreenStackState() = default;

#ifdef ANDROID
  RNSScreenStackState(RNSScreenStackState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSSearchBarState {
public:
  RNSSearchBarState() = default;

#ifdef ANDROID
  RNSSearchBarState(RNSSearchBarState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

} // namespace facebook::react