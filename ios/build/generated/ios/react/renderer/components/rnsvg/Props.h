
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <react/renderer/graphics/Color.h>
#include <react/renderer/imagemanager/primitives.h>
#include <vector>

namespace facebook::react {

struct RNSVGSvgViewAndroidNativeBackgroundAndroidStruct {
  std::string type{};
  Float color{0.0};
  bool borderless{false};
  Float rippleRadius{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGSvgViewAndroidNativeBackgroundAndroidStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_type = map.find("type");
  if (tmp_type != map.end()) {
    fromRawValue(context, tmp_type->second, result.type);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
  auto tmp_borderless = map.find("borderless");
  if (tmp_borderless != map.end()) {
    fromRawValue(context, tmp_borderless->second, result.borderless);
  }
  auto tmp_rippleRadius = map.find("rippleRadius");
  if (tmp_rippleRadius != map.end()) {
    fromRawValue(context, tmp_rippleRadius->second, result.rippleRadius);
  }
}

static inline std::string toString(const RNSVGSvgViewAndroidNativeBackgroundAndroidStruct &value) {
  return "[Object RNSVGSvgViewAndroidNativeBackgroundAndroidStruct]";
}

struct RNSVGSvgViewAndroidNativeForegroundAndroidStruct {
  std::string type{};
  Float color{0.0};
  bool borderless{false};
  Float rippleRadius{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGSvgViewAndroidNativeForegroundAndroidStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_type = map.find("type");
  if (tmp_type != map.end()) {
    fromRawValue(context, tmp_type->second, result.type);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
  auto tmp_borderless = map.find("borderless");
  if (tmp_borderless != map.end()) {
    fromRawValue(context, tmp_borderless->second, result.borderless);
  }
  auto tmp_rippleRadius = map.find("rippleRadius");
  if (tmp_rippleRadius != map.end()) {
    fromRawValue(context, tmp_rippleRadius->second, result.rippleRadius);
  }
}

static inline std::string toString(const RNSVGSvgViewAndroidNativeForegroundAndroidStruct &value) {
  return "[Object RNSVGSvgViewAndroidNativeForegroundAndroidStruct]";
}
class RNSVGSvgViewAndroidProps final : public ViewProps {
 public:
  RNSVGSvgViewAndroidProps() = default;
  RNSVGSvgViewAndroidProps(const PropsParserContext& context, const RNSVGSvgViewAndroidProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic bbWidth{};
  folly::dynamic bbHeight{};
  Float minX{0.0};
  Float minY{0.0};
  Float vbWidth{0.0};
  Float vbHeight{0.0};
  std::string align{};
  int meetOrSlice{0};
  SharedColor color{};
  std::string pointerEvents{};
  bool hasTVPreferredFocus{false};
  SharedColor borderBottomColor{};
  int nextFocusDown{0};
  SharedColor borderRightColor{};
  int nextFocusRight{0};
  SharedColor borderLeftColor{};
  SharedColor borderColor{};
  bool removeClippedSubviews{false};
  int nextFocusForward{0};
  int nextFocusUp{0};
  bool accessible{false};
  SharedColor borderStartColor{};
  SharedColor borderEndColor{};
  bool focusable{false};
  RNSVGSvgViewAndroidNativeBackgroundAndroidStruct nativeBackgroundAndroid{};
  RNSVGSvgViewAndroidNativeForegroundAndroidStruct nativeForegroundAndroid{};
  std::string backfaceVisibility{};
  std::string borderStyle{};
  bool needsOffscreenAlphaCompositing{false};
  folly::dynamic hitSlop{};
  SharedColor borderTopColor{};
  int nextFocusLeft{0};
  SharedColor borderBlockColor{};
  SharedColor borderBlockEndColor{};
  SharedColor borderBlockStartColor{};
  folly::dynamic borderRadius{};
  folly::dynamic borderTopLeftRadius{};
  folly::dynamic borderTopRightRadius{};
  folly::dynamic borderBottomRightRadius{};
  folly::dynamic borderBottomLeftRadius{};
  folly::dynamic borderTopStartRadius{};
  folly::dynamic borderTopEndRadius{};
  folly::dynamic borderBottomStartRadius{};
  folly::dynamic borderBottomEndRadius{};
  folly::dynamic borderEndEndRadius{};
  folly::dynamic borderEndStartRadius{};
  folly::dynamic borderStartEndRadius{};
  folly::dynamic borderStartStartRadius{};
};

class RNSVGCircleProps final : public ViewProps {
 public:
  RNSVGCircleProps() = default;
  RNSVGCircleProps(const PropsParserContext& context, const RNSVGCircleProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic cx{};
  folly::dynamic cy{};
  folly::dynamic r{};
};

class RNSVGClipPathProps final : public ViewProps {
 public:
  RNSVGClipPathProps() = default;
  RNSVGClipPathProps(const PropsParserContext& context, const RNSVGClipPathProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
};

class RNSVGDefsProps final : public ViewProps {
 public:
  RNSVGDefsProps() = default;
  RNSVGDefsProps(const PropsParserContext& context, const RNSVGDefsProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
};

class RNSVGEllipseProps final : public ViewProps {
 public:
  RNSVGEllipseProps() = default;
  RNSVGEllipseProps(const PropsParserContext& context, const RNSVGEllipseProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic cx{};
  folly::dynamic cy{};
  folly::dynamic rx{};
  folly::dynamic ry{};
};

enum class RNSVGFeBlendMode { Unknown, Normal, Multiply, Screen, Darken, Lighten };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGFeBlendMode &result) {
  auto string = (std::string)value;
  if (string == "unknown") { result = RNSVGFeBlendMode::Unknown; return; }
  if (string == "normal") { result = RNSVGFeBlendMode::Normal; return; }
  if (string == "multiply") { result = RNSVGFeBlendMode::Multiply; return; }
  if (string == "screen") { result = RNSVGFeBlendMode::Screen; return; }
  if (string == "darken") { result = RNSVGFeBlendMode::Darken; return; }
  if (string == "lighten") { result = RNSVGFeBlendMode::Lighten; return; }
  abort();
}

static inline std::string toString(const RNSVGFeBlendMode &value) {
  switch (value) {
    case RNSVGFeBlendMode::Unknown: return "unknown";
    case RNSVGFeBlendMode::Normal: return "normal";
    case RNSVGFeBlendMode::Multiply: return "multiply";
    case RNSVGFeBlendMode::Screen: return "screen";
    case RNSVGFeBlendMode::Darken: return "darken";
    case RNSVGFeBlendMode::Lighten: return "lighten";
  }
}

class RNSVGFeBlendProps final : public ViewProps {
 public:
  RNSVGFeBlendProps() = default;
  RNSVGFeBlendProps(const PropsParserContext& context, const RNSVGFeBlendProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  std::string in1{};
  std::string in2{};
  RNSVGFeBlendMode mode{RNSVGFeBlendMode::Normal};
};

enum class RNSVGFeColorMatrixType { Matrix, Saturate, HueRotate, LuminanceToAlpha };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGFeColorMatrixType &result) {
  auto string = (std::string)value;
  if (string == "matrix") { result = RNSVGFeColorMatrixType::Matrix; return; }
  if (string == "saturate") { result = RNSVGFeColorMatrixType::Saturate; return; }
  if (string == "hueRotate") { result = RNSVGFeColorMatrixType::HueRotate; return; }
  if (string == "luminanceToAlpha") { result = RNSVGFeColorMatrixType::LuminanceToAlpha; return; }
  abort();
}

static inline std::string toString(const RNSVGFeColorMatrixType &value) {
  switch (value) {
    case RNSVGFeColorMatrixType::Matrix: return "matrix";
    case RNSVGFeColorMatrixType::Saturate: return "saturate";
    case RNSVGFeColorMatrixType::HueRotate: return "hueRotate";
    case RNSVGFeColorMatrixType::LuminanceToAlpha: return "luminanceToAlpha";
  }
}

class RNSVGFeColorMatrixProps final : public ViewProps {
 public:
  RNSVGFeColorMatrixProps() = default;
  RNSVGFeColorMatrixProps(const PropsParserContext& context, const RNSVGFeColorMatrixProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  std::string in1{};
  RNSVGFeColorMatrixType type{RNSVGFeColorMatrixType::Matrix};
  std::vector<Float> values{};
};

enum class RNSVGFeCompositeOperator1 { Over, In, Out, Atop, Xor, Arithmetic };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGFeCompositeOperator1 &result) {
  auto string = (std::string)value;
  if (string == "over") { result = RNSVGFeCompositeOperator1::Over; return; }
  if (string == "in") { result = RNSVGFeCompositeOperator1::In; return; }
  if (string == "out") { result = RNSVGFeCompositeOperator1::Out; return; }
  if (string == "atop") { result = RNSVGFeCompositeOperator1::Atop; return; }
  if (string == "xor") { result = RNSVGFeCompositeOperator1::Xor; return; }
  if (string == "arithmetic") { result = RNSVGFeCompositeOperator1::Arithmetic; return; }
  abort();
}

static inline std::string toString(const RNSVGFeCompositeOperator1 &value) {
  switch (value) {
    case RNSVGFeCompositeOperator1::Over: return "over";
    case RNSVGFeCompositeOperator1::In: return "in";
    case RNSVGFeCompositeOperator1::Out: return "out";
    case RNSVGFeCompositeOperator1::Atop: return "atop";
    case RNSVGFeCompositeOperator1::Xor: return "xor";
    case RNSVGFeCompositeOperator1::Arithmetic: return "arithmetic";
  }
}

class RNSVGFeCompositeProps final : public ViewProps {
 public:
  RNSVGFeCompositeProps() = default;
  RNSVGFeCompositeProps(const PropsParserContext& context, const RNSVGFeCompositeProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  std::string in1{};
  std::string in2{};
  RNSVGFeCompositeOperator1 operator1{RNSVGFeCompositeOperator1::Over};
  Float k1{0.0};
  Float k2{0.0};
  Float k3{0.0};
  Float k4{0.0};
};

class RNSVGFeFloodProps final : public ViewProps {
 public:
  RNSVGFeFloodProps() = default;
  RNSVGFeFloodProps(const PropsParserContext& context, const RNSVGFeFloodProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  folly::dynamic floodColor{};
  Float floodOpacity{1.0};
};

enum class RNSVGFeGaussianBlurEdgeMode { Duplicate, Wrap, None };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGFeGaussianBlurEdgeMode &result) {
  auto string = (std::string)value;
  if (string == "duplicate") { result = RNSVGFeGaussianBlurEdgeMode::Duplicate; return; }
  if (string == "wrap") { result = RNSVGFeGaussianBlurEdgeMode::Wrap; return; }
  if (string == "none") { result = RNSVGFeGaussianBlurEdgeMode::None; return; }
  abort();
}

static inline std::string toString(const RNSVGFeGaussianBlurEdgeMode &value) {
  switch (value) {
    case RNSVGFeGaussianBlurEdgeMode::Duplicate: return "duplicate";
    case RNSVGFeGaussianBlurEdgeMode::Wrap: return "wrap";
    case RNSVGFeGaussianBlurEdgeMode::None: return "none";
  }
}

class RNSVGFeGaussianBlurProps final : public ViewProps {
 public:
  RNSVGFeGaussianBlurProps() = default;
  RNSVGFeGaussianBlurProps(const PropsParserContext& context, const RNSVGFeGaussianBlurProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  std::string in1{};
  Float stdDeviationX{0.0};
  Float stdDeviationY{0.0};
  RNSVGFeGaussianBlurEdgeMode edgeMode{RNSVGFeGaussianBlurEdgeMode::None};
};

class RNSVGFeMergeProps final : public ViewProps {
 public:
  RNSVGFeMergeProps() = default;
  RNSVGFeMergeProps(const PropsParserContext& context, const RNSVGFeMergeProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  std::vector<std::string> nodes{};
};

class RNSVGFeOffsetProps final : public ViewProps {
 public:
  RNSVGFeOffsetProps() = default;
  RNSVGFeOffsetProps(const PropsParserContext& context, const RNSVGFeOffsetProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  std::string result{};
  std::string in1{};
  folly::dynamic dx{};
  folly::dynamic dy{};
};

enum class RNSVGFilterFilterUnits { UserSpaceOnUse, ObjectBoundingBox };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGFilterFilterUnits &result) {
  auto string = (std::string)value;
  if (string == "userSpaceOnUse") { result = RNSVGFilterFilterUnits::UserSpaceOnUse; return; }
  if (string == "objectBoundingBox") { result = RNSVGFilterFilterUnits::ObjectBoundingBox; return; }
  abort();
}

static inline std::string toString(const RNSVGFilterFilterUnits &value) {
  switch (value) {
    case RNSVGFilterFilterUnits::UserSpaceOnUse: return "userSpaceOnUse";
    case RNSVGFilterFilterUnits::ObjectBoundingBox: return "objectBoundingBox";
  }
}
enum class RNSVGFilterPrimitiveUnits { UserSpaceOnUse, ObjectBoundingBox };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSVGFilterPrimitiveUnits &result) {
  auto string = (std::string)value;
  if (string == "userSpaceOnUse") { result = RNSVGFilterPrimitiveUnits::UserSpaceOnUse; return; }
  if (string == "objectBoundingBox") { result = RNSVGFilterPrimitiveUnits::ObjectBoundingBox; return; }
  abort();
}

static inline std::string toString(const RNSVGFilterPrimitiveUnits &value) {
  switch (value) {
    case RNSVGFilterPrimitiveUnits::UserSpaceOnUse: return "userSpaceOnUse";
    case RNSVGFilterPrimitiveUnits::ObjectBoundingBox: return "objectBoundingBox";
  }
}

class RNSVGFilterProps final : public ViewProps {
 public:
  RNSVGFilterProps() = default;
  RNSVGFilterProps(const PropsParserContext& context, const RNSVGFilterProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic height{};
  folly::dynamic width{};
  RNSVGFilterFilterUnits filterUnits{RNSVGFilterFilterUnits::ObjectBoundingBox};
  RNSVGFilterPrimitiveUnits primitiveUnits{RNSVGFilterPrimitiveUnits::UserSpaceOnUse};
};

class RNSVGForeignObjectProps final : public ViewProps {
 public:
  RNSVGForeignObjectProps() = default;
  RNSVGForeignObjectProps(const PropsParserContext& context, const RNSVGForeignObjectProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic height{};
  folly::dynamic width{};
};

class RNSVGGroupProps final : public ViewProps {
 public:
  RNSVGGroupProps() = default;
  RNSVGGroupProps(const PropsParserContext& context, const RNSVGGroupProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
};

class RNSVGImageProps final : public ViewProps {
 public:
  RNSVGImageProps() = default;
  RNSVGImageProps(const PropsParserContext& context, const RNSVGImageProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic width{};
  folly::dynamic height{};
  ImageSource src{};
  std::string align{};
  int meetOrSlice{0};
};

class RNSVGSvgViewProps final : public ViewProps {
 public:
  RNSVGSvgViewProps() = default;
  RNSVGSvgViewProps(const PropsParserContext& context, const RNSVGSvgViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  folly::dynamic bbWidth{};
  folly::dynamic bbHeight{};
  Float minX{0.0};
  Float minY{0.0};
  Float vbWidth{0.0};
  Float vbHeight{0.0};
  std::string align{};
  int meetOrSlice{0};
  SharedColor color{};
  std::string pointerEvents{};
  folly::dynamic hitSlop{};
};

class RNSVGLinearGradientProps final : public ViewProps {
 public:
  RNSVGLinearGradientProps() = default;
  RNSVGLinearGradientProps(const PropsParserContext& context, const RNSVGLinearGradientProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  folly::dynamic x1{};
  folly::dynamic y1{};
  folly::dynamic x2{};
  folly::dynamic y2{};
  std::vector<Float> gradient{};
  int gradientUnits{0};
  std::vector<Float> gradientTransform{};
};

class RNSVGLineProps final : public ViewProps {
 public:
  RNSVGLineProps() = default;
  RNSVGLineProps(const PropsParserContext& context, const RNSVGLineProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic x1{};
  folly::dynamic y1{};
  folly::dynamic x2{};
  folly::dynamic y2{};
};

class RNSVGMarkerProps final : public ViewProps {
 public:
  RNSVGMarkerProps() = default;
  RNSVGMarkerProps(const PropsParserContext& context, const RNSVGMarkerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic refX{};
  folly::dynamic refY{};
  folly::dynamic markerHeight{};
  folly::dynamic markerWidth{};
  std::string markerUnits{};
  std::string orient{};
  Float minX{0.0};
  Float minY{0.0};
  Float vbWidth{0.0};
  Float vbHeight{0.0};
  std::string align{};
  int meetOrSlice{0};
};

class RNSVGMaskProps final : public ViewProps {
 public:
  RNSVGMaskProps() = default;
  RNSVGMaskProps(const PropsParserContext& context, const RNSVGMaskProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic height{};
  folly::dynamic width{};
  int maskUnits{0};
  int maskContentUnits{0};
  int maskType{0};
};

class RNSVGPathProps final : public ViewProps {
 public:
  RNSVGPathProps() = default;
  RNSVGPathProps(const PropsParserContext& context, const RNSVGPathProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  std::string d{};
};

class RNSVGPatternProps final : public ViewProps {
 public:
  RNSVGPatternProps() = default;
  RNSVGPatternProps(const PropsParserContext& context, const RNSVGPatternProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic height{};
  folly::dynamic width{};
  int patternUnits{0};
  int patternContentUnits{0};
  std::vector<Float> patternTransform{};
  Float minX{0.0};
  Float minY{0.0};
  Float vbWidth{0.0};
  Float vbHeight{0.0};
  std::string align{};
  int meetOrSlice{0};
};

class RNSVGRadialGradientProps final : public ViewProps {
 public:
  RNSVGRadialGradientProps() = default;
  RNSVGRadialGradientProps(const PropsParserContext& context, const RNSVGRadialGradientProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  folly::dynamic fx{};
  folly::dynamic fy{};
  folly::dynamic cx{};
  folly::dynamic cy{};
  folly::dynamic rx{};
  folly::dynamic ry{};
  std::vector<Float> gradient{};
  int gradientUnits{0};
  std::vector<Float> gradientTransform{};
};

class RNSVGRectProps final : public ViewProps {
 public:
  RNSVGRectProps() = default;
  RNSVGRectProps(const PropsParserContext& context, const RNSVGRectProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic height{};
  folly::dynamic width{};
  folly::dynamic rx{};
  folly::dynamic ry{};
};

class RNSVGSymbolProps final : public ViewProps {
 public:
  RNSVGSymbolProps() = default;
  RNSVGSymbolProps(const PropsParserContext& context, const RNSVGSymbolProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  Float minX{0.0};
  Float minY{0.0};
  Float vbWidth{0.0};
  Float vbHeight{0.0};
  std::string align{};
  int meetOrSlice{0};
};

class RNSVGTextProps final : public ViewProps {
 public:
  RNSVGTextProps() = default;
  RNSVGTextProps(const PropsParserContext& context, const RNSVGTextProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic dx{};
  folly::dynamic dy{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic rotate{};
  folly::dynamic inlineSize{};
  folly::dynamic textLength{};
  folly::dynamic baselineShift{};
  std::string lengthAdjust{};
  std::string alignmentBaseline{};
  folly::dynamic verticalAlign{};
};

class RNSVGTextPathProps final : public ViewProps {
 public:
  RNSVGTextPathProps() = default;
  RNSVGTextPathProps(const PropsParserContext& context, const RNSVGTextPathProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic dx{};
  folly::dynamic dy{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic rotate{};
  folly::dynamic inlineSize{};
  folly::dynamic textLength{};
  folly::dynamic baselineShift{};
  std::string lengthAdjust{};
  std::string alignmentBaseline{};
  folly::dynamic verticalAlign{};
  std::string href{};
  std::string side{};
  std::string method{};
  std::string midLine{};
  std::string spacing{};
  folly::dynamic startOffset{};
};

class RNSVGTSpanProps final : public ViewProps {
 public:
  RNSVGTSpanProps() = default;
  RNSVGTSpanProps(const PropsParserContext& context, const RNSVGTSpanProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  folly::dynamic fontSize{};
  folly::dynamic fontWeight{};
  folly::dynamic font{};
  folly::dynamic dx{};
  folly::dynamic dy{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic rotate{};
  folly::dynamic inlineSize{};
  folly::dynamic textLength{};
  folly::dynamic baselineShift{};
  std::string lengthAdjust{};
  std::string alignmentBaseline{};
  folly::dynamic verticalAlign{};
  std::string content{};
};

class RNSVGUseProps final : public ViewProps {
 public:
  RNSVGUseProps() = default;
  RNSVGUseProps(const PropsParserContext& context, const RNSVGUseProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string name{};
  Float opacity{1.0};
  std::vector<Float> matrix{};
  std::string mask{};
  std::string markerStart{};
  std::string markerMid{};
  std::string markerEnd{};
  std::string clipPath{};
  int clipRule{0};
  bool responsible{false};
  std::string display{};
  std::string pointerEvents{};
  SharedColor color{};
  folly::dynamic fill{};
  Float fillOpacity{1.0};
  int fillRule{1};
  folly::dynamic stroke{};
  Float strokeOpacity{1.0};
  folly::dynamic strokeWidth{};
  int strokeLinecap{0};
  int strokeLinejoin{0};
  folly::dynamic strokeDasharray{};
  Float strokeDashoffset{0.0};
  Float strokeMiterlimit{0.0};
  int vectorEffect{0};
  std::vector<std::string> propList{};
  std::string filter{};
  std::string href{};
  folly::dynamic x{};
  folly::dynamic y{};
  folly::dynamic height{};
  folly::dynamic width{};
};

} // namespace facebook::react
