/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "rnasyncstorage.h"


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiGet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiGet", @selector(multiGet:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiSet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiSet", @selector(multiSet:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiRemove(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiRemove", @selector(multiRemove:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiMerge(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiMerge", @selector(multiMerge:callback:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_getAllKeys(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getAllKeys", @selector(getAllKeys:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_clear(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "clear", @selector(clear:), args, count);
    }

  NativeAsyncStorageModuleSpecJSI::NativeAsyncStorageModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["multiGet"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiGet};
        
        
        methodMap_["multiSet"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiSet};
        
        
        methodMap_["multiRemove"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiRemove};
        
        
        methodMap_["multiMerge"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiMerge};
        
        
        methodMap_["getAllKeys"] = MethodMetadata {1, __hostFunction_NativeAsyncStorageModuleSpecJSI_getAllKeys};
        
        
        methodMap_["clear"] = MethodMetadata {1, __hostFunction_NativeAsyncStorageModuleSpecJSI_clear};
        
  }
} // namespace facebook::react
