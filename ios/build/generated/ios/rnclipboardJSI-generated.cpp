/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "rnclipboardJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_getString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->getString(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_getStrings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->getStrings(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_getImagePNG(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->getImagePNG(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_getImageJPG(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->getImageJPG(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_setImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->setImage(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_getImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->getImage(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_setString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->setString(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_setStrings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->setStrings(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_hasString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->hasString(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_hasImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->hasImage(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_hasURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->hasURL(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_hasNumber(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->hasNumber(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_hasWebURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->hasWebURL(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_setListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->setListener(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_removeListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->removeListener(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeClipboardModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardModuleCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeClipboardModuleCxxSpecJSI::NativeClipboardModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNCClipboard", jsInvoker) {
  methodMap_["getString"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_getString};
  methodMap_["getStrings"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_getStrings};
  methodMap_["getImagePNG"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_getImagePNG};
  methodMap_["getImageJPG"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_getImageJPG};
  methodMap_["setImage"] = MethodMetadata {1, __hostFunction_NativeClipboardModuleCxxSpecJSI_setImage};
  methodMap_["getImage"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_getImage};
  methodMap_["setString"] = MethodMetadata {1, __hostFunction_NativeClipboardModuleCxxSpecJSI_setString};
  methodMap_["setStrings"] = MethodMetadata {1, __hostFunction_NativeClipboardModuleCxxSpecJSI_setStrings};
  methodMap_["hasString"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_hasString};
  methodMap_["hasImage"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_hasImage};
  methodMap_["hasURL"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_hasURL};
  methodMap_["hasNumber"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_hasNumber};
  methodMap_["hasWebURL"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_hasWebURL};
  methodMap_["setListener"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_setListener};
  methodMap_["removeListener"] = MethodMetadata {0, __hostFunction_NativeClipboardModuleCxxSpecJSI_removeListener};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeClipboardModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeClipboardModuleCxxSpecJSI_removeListeners};
}


} // namespace facebook::react
