/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "rndocumentpickerJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeDocumentPickerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDocumentPickerCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeDocumentPickerCxxSpecJSI_pick(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDocumentPickerCxxSpecJSI *>(&turboModule)->pick(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeDocumentPickerCxxSpecJSI_releaseSecureAccess(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDocumentPickerCxxSpecJSI *>(&turboModule)->releaseSecureAccess(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}
static jsi::Value __hostFunction_NativeDocumentPickerCxxSpecJSI_pickDirectory(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDocumentPickerCxxSpecJSI *>(&turboModule)->pickDirectory(
    rt
  );
}

NativeDocumentPickerCxxSpecJSI::NativeDocumentPickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNDocumentPicker", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeDocumentPickerCxxSpecJSI_getConstants};
  methodMap_["pick"] = MethodMetadata {1, __hostFunction_NativeDocumentPickerCxxSpecJSI_pick};
  methodMap_["releaseSecureAccess"] = MethodMetadata {1, __hostFunction_NativeDocumentPickerCxxSpecJSI_releaseSecureAccess};
  methodMap_["pickDirectory"] = MethodMetadata {0, __hostFunction_NativeDocumentPickerCxxSpecJSI_pickDirectory};
}


} // namespace facebook::react
