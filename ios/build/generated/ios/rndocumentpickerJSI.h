/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  
#pragma mark - NativeDocumentPickerDirectoryPickerResponse

template <typename P0>
struct NativeDocumentPickerDirectoryPickerResponse {
  P0 uri;
  bool operator==(const NativeDocumentPickerDirectoryPickerResponse &other) const {
    return uri == other.uri;
  }
};

template <typename T>
struct NativeDocumentPickerDirectoryPickerResponseBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.uri)>(rt, value.getProperty(rt, "uri"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String uriToJs(jsi::Runtime &rt, decltype(types.uri) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "uri", bridging::toJs(rt, value.uri, jsInvoker));
    return result;
  }
};



#pragma mark - NativeDocumentPickerDocumentPickerResponse

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5>
struct NativeDocumentPickerDocumentPickerResponse {
  P0 uri;
  P1 name;
  P2 copyError;
  P3 fileCopyUri;
  P4 type;
  P5 size;
  bool operator==(const NativeDocumentPickerDocumentPickerResponse &other) const {
    return uri == other.uri && name == other.name && copyError == other.copyError && fileCopyUri == other.fileCopyUri && type == other.type && size == other.size;
  }
};

template <typename T>
struct NativeDocumentPickerDocumentPickerResponseBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.uri)>(rt, value.getProperty(rt, "uri"), jsInvoker),
      bridging::fromJs<decltype(types.name)>(rt, value.getProperty(rt, "name"), jsInvoker),
      bridging::fromJs<decltype(types.copyError)>(rt, value.getProperty(rt, "copyError"), jsInvoker),
      bridging::fromJs<decltype(types.fileCopyUri)>(rt, value.getProperty(rt, "fileCopyUri"), jsInvoker),
      bridging::fromJs<decltype(types.type)>(rt, value.getProperty(rt, "type"), jsInvoker),
      bridging::fromJs<decltype(types.size)>(rt, value.getProperty(rt, "size"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String uriToJs(jsi::Runtime &rt, decltype(types.uri) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String nameToJs(jsi::Runtime &rt, decltype(types.name) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String copyErrorToJs(jsi::Runtime &rt, decltype(types.copyError) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> fileCopyUriToJs(jsi::Runtime &rt, decltype(types.fileCopyUri) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> typeToJs(jsi::Runtime &rt, decltype(types.type) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> sizeToJs(jsi::Runtime &rt, decltype(types.size) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "uri", bridging::toJs(rt, value.uri, jsInvoker));
    result.setProperty(rt, "name", bridging::toJs(rt, value.name, jsInvoker));
    if (value.copyError) {
      result.setProperty(rt, "copyError", bridging::toJs(rt, value.copyError.value(), jsInvoker));
    }
    result.setProperty(rt, "fileCopyUri", bridging::toJs(rt, value.fileCopyUri, jsInvoker));
    result.setProperty(rt, "type", bridging::toJs(rt, value.type, jsInvoker));
    result.setProperty(rt, "size", bridging::toJs(rt, value.size, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeDocumentPickerCxxSpecJSI : public TurboModule {
protected:
  NativeDocumentPickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value pick(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual jsi::Value releaseSecureAccess(jsi::Runtime &rt, jsi::Array uris) = 0;
  virtual jsi::Value pickDirectory(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeDocumentPickerCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNDocumentPicker";

protected:
  NativeDocumentPickerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDocumentPickerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDocumentPickerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDocumentPickerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value pick(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::pick) == 2,
          "Expected pick(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::pick, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value releaseSecureAccess(jsi::Runtime &rt, jsi::Array uris) override {
      static_assert(
          bridging::getParameterCount(&T::releaseSecureAccess) == 2,
          "Expected releaseSecureAccess(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::releaseSecureAccess, jsInvoker_, instance_, std::move(uris));
    }
    jsi::Value pickDirectory(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::pickDirectory) == 1,
          "Expected pickDirectory(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::pickDirectory, jsInvoker_, instance_);
    }

  private:
    friend class NativeDocumentPickerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
