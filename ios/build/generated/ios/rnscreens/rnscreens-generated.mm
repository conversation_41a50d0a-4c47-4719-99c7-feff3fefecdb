/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "rnscreens.h"


namespace facebook::react {
  

  NativeScreensModuleSpecJSI::NativeScreensModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
  }
} // namespace facebook::react
