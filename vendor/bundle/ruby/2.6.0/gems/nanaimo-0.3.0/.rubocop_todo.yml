# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2018-02-13 09:45:35 -0800 using RuboCop version 0.52.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 22
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: auto_detection, squiggly, active_support, powerpack, unindent
Layout/IndentHeredoc:
  Exclude:
    - 'Rakefile'
    - 'lib/nanaimo/writer/xml.rb'
    - 'spec/nanaimo/reader_spec.rb'
    - 'spec/nanaimo/writer/xml_spec.rb'

# Offense count: 2
# Configuration parameters: Blacklist.
# Blacklist: END, (?-mix:EO[A-Z]{1})
Naming/HeredocDelimiterNaming:
  Exclude:
    - 'lib/nanaimo/writer/xml.rb'

# Offense count: 3
# Cop supports --auto-correct.
Performance/RegexpMatch:
  Exclude:
    - 'lib/nanaimo/unicode.rb'
    - 'lib/nanaimo/writer.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: compact, expanded
Style/EmptyMethod:
  Exclude:
    - 'lib/nanaimo/writer/xml.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/Encoding:
  Exclude:
    - 'nanaimo.gemspec'

# Offense count: 2
# Configuration parameters: .
# SupportedStyles: annotated, template, unannotated
Style/FormatStringToken:
  EnforcedStyle: unannotated

# Offense count: 2
Style/IdenticalConditionalBranches:
  Exclude:
    - 'lib/nanaimo/unicode.rb'

# Offense count: 4
# Cop supports --auto-correct.
Style/IfUnlessModifier:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/unicode.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, EnforcedStyle.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Exclude:
    - 'spec/**/*'
    - 'lib/nanaimo/writer.rb'

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: PreferredDelimiters.
Style/PercentLiteralDelimiters:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/unicode.rb'
    - 'spec/nanaimo/reader_spec.rb'
    - 'spec/nanaimo/unicode_spec.rb'
    - 'spec/nanaimo/writer/xml_spec.rb'
    - 'spec/nanaimo/writer_spec.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/RedundantFreeze:
  Exclude:
    - 'lib/nanaimo/reader.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: MinSize.
# SupportedStyles: percent, brackets
Style/SymbolArray:
  EnforcedStyle: brackets

# Offense count: 56
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Metrics/LineLength:
  Max: 331
