# -*- encoding: utf-8 -*-
# stub: ruby-macho 2.5.1 ruby lib

Gem::Specification.new do |s|
  s.name = "ruby-macho".freeze
  s.version = "2.5.1"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2021-05-15"
  s.description = "A library for viewing and manipulating Mach-O files in Ruby.".freeze
  s.email = "<EMAIL>".freeze
  s.homepage = "https://github.com/Homebrew/ruby-macho".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.5".freeze)
  s.rubygems_version = "*******".freeze
  s.summary = "ruby-macho - Mach-O file analyzer.".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version
end
